import { Injectable, inject } from '@angular/core';
import { AssessmentScoringService } from './assessment-scoring.service';

@Injectable({
  providedIn: 'root',
})
export class ReportProcessorService {
  private scoringService = inject(AssessmentScoringService);

  /** Inserted by Angular inject() migration for backwards compatibility */
  constructor(...args: unknown[]);

  constructor() {}

  /**
   * Process SHAPE assessment results into comprehensive report
   */
  processShapeReport(rawResults: any): any {
    // Check if assessment data is complete
    if (!this.isAssessmentDataComplete(rawResults)) {
      return null; // Will trigger warning in component
    }

    // Generate the comprehensive report structure
    const processedReport = {
      // 1. OVERALL SHAPE REPORT 🎯
      overall_report: {
        profil_integratif: this.generateIntegrativeProfile(rawResults),
        peta_interaksi: this.generateComponentInteractionMap(rawResults),
        zona_genius: this.generateGeniusZone(rawResults),
        arketipe_personal: this.generatePersonalArchetype(rawResults),
        cetak_biru_pengembangan: this.generateYearlyBlueprint(rawResults),
        wisdom_quote: this.selectWisdomQuote(rawResults),
        executive_summary: this.generateExecutiveSummary(rawResults),
      },

      // 2. DETAILED AREA REPORTS 📊
      detailed_reports: {
        strength_report: this.generateDetailedStrengthReport(
          rawResults.strength,
          rawResults
        ),
        heart_report: this.generateDetailedHeartReport(
          rawResults.heart,
          rawResults
        ),
        abilities_report: this.generateDetailedAbilitiesReport(
          rawResults.abilities,
          rawResults
        ),
        personality_report: this.generateDetailedPersonalityReport(
          rawResults.personality,
          rawResults
        ),
        experience_report: this.generateDetailedExperienceReport(
          rawResults.experience,
          rawResults
        ),
      },

      // Supporting data and backward compatibility
      categories: this.processHighScoreCategories(rawResults.categories || []),
      feedback: this.generateComprehensiveFeedback(rawResults),

      // Backward compatibility for existing components
      integrative_profile:
        this.generateIntegrativeProfile(rawResults).profileText,
      genius_zone: this.generateGeniusZone(rawResults),
      development_plan: this.createDevelopmentPlan(rawResults),
      strength_profile: this.processStrengthProfile(rawResults.strength),
      heart_profile: this.processHeartProfile(rawResults.heart),
      abilities_profile: this.processAbilitiesProfile(rawResults.abilities),
      personality_profile: this.processPersonalityProfile(
        rawResults.personality
      ),
      experience_profile: this.processExperienceProfile(rawResults.experience),
    };

    return processedReport;
  }

  /**
   * Check if assessment data is complete for report generation
   */
  private isAssessmentDataComplete(results: any): boolean {
    console.log('Checking if assessment data is complete:', results);

    if (!results || typeof results !== 'object') {
      console.log('Results are null or not an object');
      return false;
    }

    const requiredComponents = [
      'strength',
      'heart',
      'abilities',
      'personality',
      'experience',
    ];

    for (const component of requiredComponents) {
      if (!results[component] || Object.keys(results[component]).length === 0) {
        console.log(`Missing component or empty data: ${component}`);
        return false;
      }
    }

    if (
      !results.categories ||
      !Array.isArray(results.categories) ||
      results.categories.length === 0
    ) {
      console.log('Categories are missing, not an array, or empty');
      return false;
    }

    console.log('Assessment data is complete');
    return true;
  }

  /**
   * Process categories with high scores and provide detailed explanations
   */
  private processHighScoreCategories(categories: any[]): any[] {
    return categories
      .filter((cat) => cat.score >= 70) // High score threshold
      .map((cat) => ({
        ...cat,
        explanation: this.getCategoryExplanation(cat.name, cat.score),
        insights: this.getCategoryInsights(cat.name, cat.score),
      }))
      .sort((a, b) => b.score - a.score); // Sort by score descending
  }

  /**
   * Get detailed explanation for high-scoring categories
   */
  private getCategoryExplanation(categoryName: string, score: number): string {
    const explanations: { [key: string]: string } = {
      Communication: `Dengan skor ${score}%, Anda menunjukkan kemampuan luar biasa dalam "menyulam realitas" - menyambung fragmen informasi menjadi narasi kohesif. Anda memandang keheningan sebagai ruang kosong untuk diisi makna dan mampu mengubah kebisingan menjadi simfoni.`,

      Leadership: `Skor ${score}% menunjukkan Anda adalah pemimpin natural yang mampu membaca kebutuhan audiens dan memobilisasi tim menuju perubahan. Seperti "Inspiring Connector", Anda ahli menemukan kesamaan dari perbedaan dan membangun aliansi strategis.`,

      Creativity: `Dengan skor ${score}%, Anda memiliki mata seni yang memandang dunia sebagai palet emosi yang hidup. Anda mampu mengubah rutinitas menjadi pertunjukan dan mengkomunikasikan yang tak terucap melalui metafora - sebuah "terapi kejut estetika".`,

      'Problem Solving': `Skor ${score}% mencerminkan kemampuan Anda sebagai "Insightful Truth-Seeker" yang memiliki analisis tajam untuk mencari akar masalah. Anda memiliki radar untuk mendeteksi ketidakkonsistenan dan bias tersembunyi.`,

      Collaboration: `Dengan skor ${score}%, Anda adalah "Community Architect" yang mampu menciptakan lingkungan psikologis aman di mana orang bisa berkembang maksimal. Anda memiliki radar tinggi untuk kebutuhan emosional tim.`,

      Innovation: `Skor ${score}% menunjukkan Anda adalah "Visionary Pioneer" dengan pola pikir futuristik. Anda secara natural melihat kemungkinan di tempat orang lain melihat jalan buntu dan berani mengambil risiko terukur.`,

      'Technical Skills': `Dengan skor ${score}%, Anda memiliki kemampuan "Detail Master" yang memastikan akurasi dan kualitas. Anda sistematis dalam pendekatan dan memiliki kemampuan problem solving teknis yang kuat.`,

      'Analytical Thinking': `Skor ${score}% mencerminkan kemampuan kognitif Anda yang tajam. Seperti "Clarifying Mentor", Anda mampu menyusun pengetahuan sistematis dan mengubah kompleksitas menjadi kejelasan.`,
    };

    return (
      explanations[categoryName] ||
      `Dengan skor ${score}%, Anda menunjukkan keunggulan yang signifikan dalam ${categoryName}. Ini adalah salah satu kekuatan natural Anda yang dapat dikembangkan lebih lanjut.`
    );
  }

  /**
   * Get actionable insights for high-scoring categories
   */
  private getCategoryInsights(categoryName: string, score: number): string[] {
    const insights: { [key: string]: string[] } = {
      Communication: [
        'Kembangkan kemampuan storytelling untuk berbagai audiens',
        'Latih teknik active listening dan empathetic communication',
        'Ciptakan konten yang menghubungkan ide kompleks dengan bahasa sederhana',
      ],

      Leadership: [
        'Fokus pada authentic leadership style yang sesuai kepribadian',
        'Bangun kemampuan delegation dan team empowerment',
        'Kembangkan emotional intelligence untuk memimpin dengan empati',
      ],

      Creativity: [
        'Eksplor medium kreatif baru untuk memperluas ekspresi',
        'Gabungkan kreativitas dengan problem solving praktis',
        'Ciptakan lingkungan yang mendukung kreativitas berkelanjutan',
      ],

      'Problem Solving': [
        'Latih systematic thinking dan root cause analysis',
        'Kembangkan kemampuan pattern recognition',
        'Praktikkan design thinking untuk solusi inovatif',
      ],

      Collaboration: [
        'Pelajari different collaboration styles untuk berbagai tim',
        'Kembangkan conflict resolution skills',
        'Praktikkan inclusive leadership approach',
      ],

      Innovation: [
        'Buat sistem untuk capture dan develop ide-ide baru',
        'Latih rapid prototyping dan testing mindset',
        'Bangun network dengan fellow innovators',
      ],
    };

    return (
      insights[categoryName] || [
        `Manfaatkan kekuatan ${categoryName} untuk menciptakan value unik`,
        'Cari mentor atau coach yang dapat membantu mengembangkan area ini',
        'Praktikkan skill ini dalam proyek nyata untuk memperdalam kemampuan',
      ]
    );
  }

  /**
   * Generate integrative SHAPE profile with nature metaphors
   */
  private generateIntegrativeProfile(results: any): any {
    const strengthMetaphor = this.getStrengthMetaphor(
      results.strength?.primary
    );
    const heartDomain = results.heart?.primary || 'Personal Development';
    const abilityType = results.abilities?.primary || 'Cognitive Skills';
    const personalityStyle = results.personality?.type || 'Balanced Individual';
    const experiencePattern = results.experience?.pattern || 'Adaptive Learner';
    const archetype = this.generateArchetype(results);

    // Create profile text according to Overall.md guidance
    const profileText = `<strong>${
      results.userName || 'Anda'
    }</strong> adalah <em>${strengthMetaphor}</em> di bidang <strong>${heartDomain}</strong>. 
            Dengan kekuatan <strong>${
              results.strength?.primary || 'Natural Talent'
            }</strong> dan kemampuan <strong>${abilityType}</strong>, 
            gaya <strong>${personalityStyle}</strong> Anda menjelma dalam <strong>${experiencePattern}</strong>. 
            Pola unik ini membentuk arketipe khusus <strong>'${archetype}'</strong>.`;

    // Add reflection question as suggested in the guidance
    const reflectionQuestion = `Bagaimana jika Anda menginvestasikan 70% energi pada arketipe '${archetype}' ini?`;

    // Add philosophical quote
    const philosophicalQuote = {
      text: 'Alon-alon asal kelakon',
      meaning: 'Perlahan namun pasti',
      application: `Menerapkan pendekatan ${strengthMetaphor} dengan kesabaran akan membawa hasil terbaik.`,
    };

    return {
      profileText,
      strengthMetaphor,
      heartDomain,
      abilityType,
      personalityStyle,
      experiencePattern,
      archetype,
      reflectionQuestion,
      philosophicalQuote,
    };
  }

  /**
   * Process Strength profile based on comprehensive guidance
   */
  private processStrengthProfile(strengthData: any): any {
    if (!strengthData) return null;

    const strengthTypes: { [key: string]: any } = {
      'Visionary Pioneers': {
        description:
          'Pola pikir futuristik & berorientasi pada kemungkinan baru. Berani mengambil risiko terukur dan tidak nyaman dengan rutinitas.',
        superpower:
          'Anda secara natural melihat kemungkinan di tempat orang lain melihat jalan buntu.',
        teamContribution:
          'Sebagai Innovation Catalyst, Anda membantu tim beradaptasi sebelum disrupsi terjadi.',
        growthOpportunity:
          'Kembangkan kemampuan implementasi dan evaluasi proyek yang sistematis.',
        watchOut: 'Hati-hati meremehkan kompleksitas implementasi.',
        powerPartners: 'Detail-Oriented Implementers',
        famousFigures: ['Elon Musk', 'Steve Jobs', 'Nikola Tesla'],
      },
      'Insightful Truth-Seekers': {
        description:
          'Kepekaan tinggi pada ketidaksesuaian dengan dorongan kuat untuk integritas & keadilan. Analisis akar masalah yang tajam.',
        superpower:
          'Anda memiliki radar untuk mendeteksi ketidakkonsistenan dan bias tersembunyi.',
        teamContribution:
          'Sebagai Ethical Guardian, Anda melindungi tim dari keputusan yang merugikan jangka panjang.',
        growthOpportunity:
          'Latih kemampuan menyampaikan kritik dengan solusi konstruktif.',
        watchOut:
          'Jangan terlalu fokus pada masalah tanpa menawarkan alternatif.',
        powerPartners: 'Inspiring Connectors',
        famousFigures: ['Warren Buffett', 'Mahatma Gandhi', 'Maya Angelou'],
      },
      'Inspiring Connectors': {
        description:
          'Kemampuan membaca kebutuhan audiens dengan energi tinggi dalam interaksi sosial. Ahli menemukan kesamaan dari perbedaan.',
        superpower:
          'Anda mengubah ide individual menjadi gerakan kolektif melalui storytelling yang kuat.',
        teamContribution:
          'Sebagai Community Architect, Anda membangun ekosistem yang saling mendukung.',
        growthOpportunity:
          'Fokus pada konsistensi dan follow-through dalam komitmen.',
        watchOut:
          'Hati-hati dengan over-promise untuk menyenangkan semua orang.',
        powerPartners: 'Detail Masters',
        famousFigures: ['Oprah Winfrey', 'Tony Robbins', 'Barack Obama'],
      },
      'Supportive Nurturers': {
        description:
          'Radar tinggi untuk kebutuhan emosional dengan kesabaran dalam proses perkembangan. Fokus pada kesejahteraan jangka panjang.',
        superpower:
          'Anda mampu menciptakan lingkungan yang aman secara psikologis di mana orang bisa berkembang.',
        teamContribution:
          'Sebagai Talent Developer, Anda mengidentifikasi dan mengembangkan potensi dalam tim.',
        growthOpportunity:
          'Kembangkan kemampuan untuk membuat keputusan tegas saat diperlukan.',
        watchOut: 'Hindari mengabaikan kebutuhan diri sendiri demi orang lain.',
        powerPartners: 'Visionary Pioneers',
        famousFigures: ['Mother Teresa', 'Fred Rogers', 'Michelle Obama'],
      },
      'Clarifying Mentors': {
        description:
          'Bakat menyusun pengetahuan sistematis dengan kesabaran dalam repetisi pengajaran. Fokus pada keterpahaman bukan kesepakatan.',
        superpower:
          'Anda mampu menyederhanakan konsep kompleks menjadi langkah-langkah yang mudah diikuti.',
        teamContribution:
          'Sebagai Knowledge Architect, Anda membangun sistem pengetahuan yang membantu tim berkembang.',
        growthOpportunity:
          'Latih keterbukaan terhadap metode baru dan pendekatan yang berbeda.',
        watchOut: 'Hindari terlalu kaku pada metode yang sudah bekerja.',
        powerPartners: 'Inspiring Connectors',
        famousFigures: ['Peter Drucker', 'Maria Montessori', 'Albert Einstein'],
      },
    };

    // Define strength combinations and their meanings
    const strengthCombinations: { [key: string]: any } = {
      'Pioneer + Truth-Seeker': {
        name: 'Innovator Strategis',
        description: 'Visi radikal berbasis prinsip fundamental',
        example: 'Elon Musk',
        combinations: ['Visionary Pioneers', 'Insightful Truth-Seekers'],
      },
      'Connector + Nurturer': {
        name: 'Community Architect',
        description: 'Membangun ekosistem saling mendukung',
        example: 'Oprah Winfrey',
        combinations: ['Inspiring Connectors', 'Supportive Nurturers'],
      },
      'Mentor + Truth-Seeker': {
        name: 'Systems Philosopher',
        description: 'Kerangka kerja berbasis kebijaksanaan',
        example: 'Peter Drucker',
        combinations: ['Clarifying Mentors', 'Insightful Truth-Seekers'],
      },
      'Pioneer + Connector': {
        name: 'Movement Starter',
        description: 'Mengubah ide menjadi gerakan masif',
        example: 'Steve Jobs',
        combinations: ['Visionary Pioneers', 'Inspiring Connectors'],
      },
      'Nurturer + Mentor': {
        name: 'Talent Alchemist',
        description: 'Mengubah potensi mentah menjadi keunggulan',
        example: 'Mr. Miyagi in Karate Kid',
        combinations: ['Supportive Nurturers', 'Clarifying Mentors'],
      },
    };

    // Get primary and secondary strengths
    const primaryStrength = strengthData.primary || 'Balanced Individual';
    const secondaryStrength =
      strengthData.secondary || strengthData.second_highest || null;

    // Get primary strength profile data
    const primaryProfile = strengthTypes[primaryStrength] || {
      description:
        'Individu dengan kombinasi kekuatan yang seimbang dan adaptif',
      superpower:
        'Anda memiliki kemampuan untuk beradaptasi dengan berbagai situasi.',
      teamContribution:
        'Sebagai Versatile Contributor, Anda dapat mengisi berbagai peran sesuai kebutuhan.',
      growthOpportunity:
        'Identifikasi dan fokuskan pada kekuatan dominan Anda.',
      watchOut: 'Hati-hati menjadi generalis tanpa keahlian yang mendalam.',
      powerPartners: 'Specialists in complementary areas',
      famousFigures: ['Leonardo da Vinci', 'Benjamin Franklin'],
    };

    // Get secondary strength profile data if available
    const secondaryProfile = secondaryStrength
      ? strengthTypes[secondaryStrength] || null
      : null;

    // Check if there's a valid strength combination
    let strengthCombination = null;
    if (primaryStrength && secondaryStrength) {
      // Check all possible combinations
      for (const key in strengthCombinations) {
        const combo = strengthCombinations[key];
        if (
          combo.combinations.includes(primaryStrength) &&
          combo.combinations.includes(secondaryStrength)
        ) {
          strengthCombination = {
            name: combo.name,
            description: combo.description,
            example: combo.example,
          };
          break;
        }
      }
    }

    return {
      primary: primaryStrength,
      secondary: secondaryStrength,
      primaryProfile: primaryProfile,
      secondaryProfile: secondaryProfile,
      strengthCombination: strengthCombination,
      score: strengthData.score || 0,
    };
  }

  /**
   * Process Heart profile with detailed guidance explanations
   */
  private processHeartProfile(heartData: any): any {
    if (!heartData) return null;

    const heartTypes: { [key: string]: any } = {
      'Art & Entertainment': {
        description:
          'Memandang dunia sebagai palet emosi yang hidup. Mengubah rutinitas menjadi pertunjukan dan mengkomunikasikan yang tak terucap melalui metafora.',
        hiddenPower:
          'Kemampuan menyembuhkan melalui "terapi kejut estetika" - menggunakan keindahan tak terduga untuk memutus spiral pikiran negatif',
        warning:
          'Risiko "Keracunan Keindahan" - kehilangan kemampuan melihat nilai dalam yang biasa-biasa saja',
        inspirationalFigures: [
          'Puck (Midsummer Night Dream)',
          'Baron Munchausen',
          'Scheherazade',
        ],
      },
      Business: {
        description:
          'Melihat masalah sebagai model bisnis yang belum lahir. Memandang hubungan manusia sebagai jaringan nilai.',
        hiddenPower:
          'Kemampuan menciptakan "virus kemakmuran" - sistem yang menyebarkan kesejahteraan melalui interaksi alami',
        warning:
          'Bahaya "Logika Kalkulator" - mengukur segala sesuatu termasuk yang tak seharusnya terukur',
        inspirationalFigures: [
          'Midas (versi positif)',
          'Hermes',
          'Sisyphus yang Bahagia',
        ],
      },
      Education: {
        description:
          'Memandang ketidaktahuan sebagai lahan subur. Melihat kurikulum dalam alur kehidupan dan mengubah kebosanan menjadi keingintahuan.',
        hiddenPower:
          'Kemampuan "penyambung titik tak terlihat" - menemukan pola dalam pengetahuan yang tampak acak',
        warning:
          'Bahaya "Kutukan Kepastian" - kehilangan keajaiban dalam proses mencari jawaban',
        inspirationalFigures: ['Prometheus', 'Sokrates', 'Thoth'],
      },
    };

    const primaryHeart = heartData.primary || 'Personal Development';
    const profile = heartTypes[primaryHeart] || {
      description:
        'Passion yang mendalam untuk pengembangan diri dan pertumbuhan berkelanjutan',
      hiddenPower:
        'Kemampuan beradaptasi dan menemukan makna dalam berbagai situasi',
      warning: 'Jangan terlalu sering berganti fokus tanpa mendalami satu area',
      inspirationalFigures: ['Leonardo da Vinci', 'Carl Jung'],
    };

    return {
      primary: primaryHeart,
      ...profile,
      score: heartData.score || 0,
    };
  }

  /**
   * Other processing methods (simplified for space)
   */
  private processAbilitiesProfile(abilitiesData: any): any {
    if (!abilitiesData) return null;
    return {
      primary: abilitiesData.primary || 'Cognitive Skills',
      description: 'Kemampuan yang telah terbukti melalui assessment',
      score: abilitiesData.score || 0,
    };
  }

  private processPersonalityProfile(personalityData: any): any {
    if (!personalityData) return null;
    return {
      type: personalityData.type || 'Balanced Individual',
      description: 'Gaya kepribadian yang dominan',
      score: personalityData.score || 0,
    };
  }

  private processExperienceProfile(experienceData: any): any {
    if (!experienceData) return null;
    return {
      pattern: experienceData.pattern || 'Adaptive Learner',
      insight: experienceData.insight || 'Terus belajar dan beradaptasi',
    };
  }

  /**
   * Identify Genius Zone - This is now handled by generateGeniusZone
   * Keeping for backward compatibility
   */
  private identifyGeniusZone(results: any): any[] {
    return this.generateGeniusZone(results);
  }

  /**
   * Create Development Plan - This is now handled by generateDevelopmentChain
   * Keeping for backward compatibility
   */
  private createDevelopmentPlan(results: any): any[] {
    const developmentChain = this.generateDevelopmentChain(results);
    return developmentChain.map((item) => ({
      title: item.phase,
      description: item.action + ': ' + item.project,
      timeline: item.timeline,
    }));
  }

  private selectWisdomQuote(results: any): string {
    return 'Alon-alon asal kelakon - Perlahan namun pasti';
  }

  private generateComprehensiveFeedback(results: any): any {
    // Create a comprehensive feedback that integrates all aspects of SHAPE
    const strength = results.strength?.primary || 'Natural Strength';
    const heart = results.heart?.primary || 'Core Passion';
    const ability = results.abilities?.primary || 'Key Ability';
    const personality = results.personality?.type || 'Personality Style';
    const experience = results.experience?.pattern || 'Experience Pattern';

    // Get highest scoring categories
    const topCategories = this.processHighScoreCategories(
      results.categories || []
    )
      .slice(0, 3)
      .map((cat) => cat.name)
      .join(', ');

    const htmlContent = `<div class="comprehensive-feedback">
      <h4>Insight Mendalam tentang Profil SHAPE Anda</h4>
      <p>Berdasarkan hasil assessment yang lengkap, Anda menunjukkan pola unik yang mencerminkan 
      potensi luar biasa dalam mengintegrasikan berbagai dimensi SHAPE.</p>
      
      <p>Kekuatan utama Anda sebagai <strong>${strength}</strong> berpadu dengan passion di bidang 
      <strong>${heart}</strong> menciptakan fondasi karakter yang kuat. Kemampuan Anda dalam 
      <strong>${ability}</strong> diperkuat dengan gaya <strong>${personality}</strong> yang 
      mencerminkan pendekatan unik Anda dalam menghadapi tantangan.</p>
      
      <p>Area di mana Anda menunjukkan keunggulan signifikan adalah: <strong>${topCategories}</strong>. 
      Ini adalah bidang di mana Anda dapat memberikan kontribusi tertinggi dan mencapai 
      potensi maksimal.</p>
      
      <p>Pola pengalaman sebagai <strong>${experience}</strong> menunjukkan cara Anda mengintegrasikan 
      pengetahuan dan keterampilan baru ke dalam repertoar profesional Anda.</p>
    </div>`;

    // Include executive summary for a one-page overview
    const executiveSummary = {
      title: 'SHAPE Profile Executive Summary',
      strength: `Kekuatan: ${strength}`,
      heart: `Passion: ${heart}`,
      abilities: `Kemampuan: ${ability}`,
      personality: `Gaya: ${personality}`,
      experience: `Pola: ${experience}`,
      topCategories: `Area Unggulan: ${topCategories}`,
      archetype: `Arketipe: ${this.generateArchetype(results)}`,
    };

    // Include audio script summary for daily motivation (as mentioned in Overall.md)
    const audioScript = `
      Selamat pagi! Ini adalah pengingat SHAPE harian Anda.
      
      Anda memiliki kekuatan istimewa sebagai ${strength}.
      Gunakan passion Anda di bidang ${heart} untuk memandu keputusan hari ini.
      Ingat untuk memanfaatkan kemampuan ${ability} Anda dalam menghadapi tantangan.
      
      Fokus pada area unggulan Anda: ${topCategories}.
      
      Hari ini, ambil satu langkah kecil menuju cetak biru SHAPE Anda.
      Anda adalah ${this.generateArchetype(
        results
      )}. Jadilah versi terbaik dari diri Anda hari ini!
    `;

    return {
      htmlContent,
      executiveSummary,
      audioScript,
    };
  }

  private getStrengthMetaphor(strength: string): string {
    const metaphors: { [key: string]: string } = {
      'Visionary Pioneers': 'Sungai yang Membuka Lembah Baru',
      'Insightful Truth-Seekers': 'Mercusuar Kebijaksanaan',
      'Inspiring Connectors': 'Jembatan yang Menghubungkan Pulau-pulau',
    };
    return metaphors[strength] || 'Penjelajah Potensial';
  }

  private generateArchetype(results: any): string {
    const strength = results.strength?.primary;
    const heart = results.heart?.primary;

    if (strength === 'Visionary Pioneers' && heart === 'Education') {
      return 'Guru Visioner';
    }
    if (strength === 'Inspiring Connectors' && heart === 'Business') {
      return 'Entrepreneur Sosial';
    }

    return 'Catalyst Transformasi';
  }

  /**
   * Generate Component Interaction Map based on results from all assessment areas
   */
  private generateComponentInteractionMap(results: any): any {
    const patterns = {
      strengthToAbilities: this.analyzeStrengthAbilityPattern(results),
      heartToExperience: this.analyzeHeartExperiencePattern(results),
      personalityToStrength: this.analyzePersonalityStrengthPattern(results),
    };

    // Create narrative text based on patterns
    const narrative = `Passion di ${
      results.heart?.primary || 'bidang utama'
    } menjadi bahan bakar pengembangan ${
      results.abilities?.primary || 'kemampuan spesifik'
    }, yang diperkuat oleh ${results.strength?.primary || 'kekuatan bawaan'}. 
    Pola ini konsisten terlihat di pengalaman ${
      results.experience?.pattern || 'profesional'
    } Anda, dan didukung oleh kecenderungan ${
      results.personality?.type || 'kepribadian'
    }.`;

    return {
      patterns,
      narrative,
      // This would include data for a mermaid diagram in the frontend
      diagramData: {
        nodes: [
          { id: 'S', label: 'Strength', data: results.strength?.primary },
          { id: 'H', label: 'Heart', data: results.heart?.primary },
          { id: 'A', label: 'Abilities', data: results.abilities?.primary },
          { id: 'P', label: 'Personality', data: results.personality?.type },
          { id: 'E', label: 'Experience', data: results.experience?.pattern },
        ],
        links: [
          { source: 'S', target: 'E' },
          { source: 'H', target: 'A' },
          { source: 'P', target: 'H' },
          { source: 'E', target: 'P' },
          { source: 'A', target: 'S' },
        ],
      },
    };
  }

  /**
   * Analyze the pattern between Strength and Abilities
   */
  private analyzeStrengthAbilityPattern(results: any): string {
    const strength = results.strength?.primary;
    const ability = results.abilities?.primary;

    if (strength === 'Visionary Pioneers' && ability === 'Cognitive Skills') {
      return 'Kemampuan berpikir inovatif diperkuat oleh visi ke depan';
    }
    if (
      strength === 'Insightful Truth-Seekers' &&
      ability === 'Analytical Thinking'
    ) {
      return 'Analisis mendalam diperkuat oleh kemampuan mencari kebenaran';
    }

    return `${strength || 'Kekuatan'} mendukung pengembangan ${
      ability || 'kemampuan'
    } dengan cara unik`;
  }

  /**
   * Analyze the pattern between Heart and Experience
   */
  private analyzeHeartExperiencePattern(results: any): string {
    const heart = results.heart?.primary;
    const experience = results.experience?.pattern;

    if (heart === 'Education' && experience === 'Adaptive Learner') {
      return 'Passion di bidang pendidikan tercermin dalam pola pembelajaran adaptif';
    }

    return `Passion di bidang ${heart || 'utama'} tercermin dalam pengalaman ${
      experience || 'profesional'
    } Anda`;
  }

  /**
   * Analyze the pattern between Personality and Strength
   */
  private analyzePersonalityStrengthPattern(results: any): string {
    const personality = results.personality?.type;
    const strength = results.strength?.primary;

    if (
      personality === 'Inspiring Connector' &&
      strength === 'Inspiring Connectors'
    ) {
      return 'Kepribadian dan kekuatan natural Anda sangat selaras';
    }

    return `Gaya ${personality || 'kepribadian'} Anda melengkapi ${
      strength || 'kekuatan natural'
    } dengan cara yang saling memperkuat`;
  }

  /**
   * Generate detailed Genius Zone - areas where user excels based on SHAPE profile
   */
  private generateGeniusZone(results: any): any[] {
    // Create based on strength + heart combinations
    const strength = results.strength?.primary || '';
    const heart = results.heart?.primary || '';
    const personality = results.personality?.type || '';

    // Default zone
    const defaultZone = {
      field: 'Personal Development',
      reason: 'Area pengembangan potensial berdasarkan profil SHAPE',
      evidence: 'Konvergensi dari pola assessment Anda',
    };

    // Generate zones based on combinations
    const zones = [];

    if (strength === 'Visionary Pioneers' && heart === 'Education') {
      zones.push({
        field: 'Educational Innovation',
        reason: 'Strength Visionary + Passion Education + ' + personality,
        evidence: 'Pattern terlihat di proyek inovatif pendidikan',
      });
    }

    if (strength === 'Insightful Truth-Seekers' && heart === 'Business') {
      zones.push({
        field: 'Ethical Business Consulting',
        reason: 'Strength Truth-Searcher + Passion Business + ' + personality,
        evidence: 'Kemampuan mengidentifikasi masalah etika bisnis',
      });
    }

    if (
      strength === 'Inspiring Connectors' &&
      heart === 'Art & Entertainment'
    ) {
      zones.push({
        field: 'Community-Based Creative Projects',
        reason: 'Strength Connector + Passion Art + ' + personality,
        evidence: 'Proyek kolaborasi kreatif dalam portfolio',
      });
    }

    // Add at least one zone if none matched
    if (zones.length === 0) {
      zones.push(defaultZone);

      // Add a second zone based on high-scoring categories
      const highestCategory = this.getHighestScoringCategory(
        results.categories || []
      );
      if (highestCategory) {
        zones.push({
          field: highestCategory.name + ' Leadership',
          reason: 'Skor tinggi di ' + highestCategory.name + ' + ' + strength,
          evidence: 'Performa konsisten di area ' + highestCategory.name,
        });
      }
    }

    return zones;
  }

  /**
   * Get highest scoring category from assessment
   */
  private getHighestScoringCategory(categories: any[]): any {
    if (!categories || categories.length === 0) return null;
    return categories.sort((a, b) => b.score - a.score)[0];
  }

  /**
   * Generate Development Chain - prioritized development recommendations
   */
  private generateDevelopmentChain(results: any): any[] {
    const strength = results.strength?.primary || 'Natural Strengths';
    const heart = results.heart?.primary || 'Passion Areas';
    const ability = results.abilities?.primary || 'Core Abilities';
    const personality = results.personality?.type || 'Personality Style';
    const experience = results.experience?.pattern || 'Experience Pattern';

    return [
      {
        phase: 'Leverage Strength',
        action: `Aktifkan ${strength}`,
        project: this.getStrengthProject(strength),
        timeline: '90 Hari',
      },
      {
        phase: 'Fuel dengan Heart',
        action: `Eksplorasi ${heart}`,
        project: this.getHeartProject(heart),
        timeline: '60 Hari',
      },
      {
        phase: 'Amplifikasi Ability',
        action: `Tingkatkan ${ability}`,
        project: this.getAbilityProject(ability),
        timeline: '45 Hari',
      },
      {
        phase: 'Adapt Personality',
        action: `Manfaatkan ${personality}`,
        project: this.getPersonalityProject(personality),
        timeline: '30 Hari',
      },
      {
        phase: 'Integrasi Experience',
        action: `Bangun berdasarkan ${experience}`,
        project: this.getExperienceProject(experience),
        timeline: '30-90 Hari',
      },
    ];
  }

  /**
   * Get project recommendation based on strength
   */
  private getStrengthProject(strength: string): string {
    const projects: { [key: string]: string } = {
      'Visionary Pioneers': 'Design Thinking Workshop untuk inovasi bisnis',
      'Insightful Truth-Seekers': 'Audit etika dan transparansi organisasi',
      'Inspiring Connectors': 'Membangun jaringan kolaborasi lintas sektor',
    };
    return projects[strength] || 'Proyek yang memanfaatkan kekuatan alami Anda';
  }

  /**
   * Get project recommendation based on heart
   */
  private getHeartProject(heart: string): string {
    const projects: { [key: string]: string } = {
      'Art & Entertainment': 'Workshop seni ekspresif untuk komunitas',
      Business: 'Membangun model bisnis dengan impact sosial',
      Education: 'Program mentoring untuk transfer pengetahuan',
    };
    return projects[heart] || 'Eksplorasi area yang paling Anda passionate';
  }

  /**
   * Get project recommendation based on ability
   */
  private getAbilityProject(ability: string): string {
    const projects: { [key: string]: string } = {
      'Cognitive Skills': 'Pelatihan strategic thinking dan decision making',
      'Analytical Thinking': 'Kursus data analysis dan visualization',
      'Technical Skills': 'Sertifikasi keterampilan teknis terbaru',
    };
    return projects[ability] || 'Peningkatan keterampilan inti';
  }

  /**
   * Get project recommendation based on personality
   */
  private getPersonalityProject(personality: string): string {
    const projects: { [key: string]: string } = {
      'Inspiring Connector': 'Public speaking dan storytelling workshop',
      'Detail Master': 'Project management certification',
      'Visionary Leader': 'Future mapping dan scenario planning',
    };
    return projects[personality] || 'Proyek yang sesuai gaya personal Anda';
  }

  /**
   * Get project recommendation based on experience pattern
   */
  private getExperienceProject(experience: string): string {
    const projects: { [key: string]: string } = {
      'Adaptive Learner': 'Cross-functional project leadership',
      Specialist: 'Mentoring junior dalam bidang keahlian',
      Generalist: 'Integrasi multi-disciplinary knowledge',
    };
    return (
      projects[experience] || 'Pengembangan berdasarkan pola pengalaman Anda'
    );
  }

  /**
   * Generate Uniqueness Defense - early warning system for potential pitfalls
   */
  private generateUniquenessDefense(results: any): any {
    const strength = results.strength?.primary || '';
    const personality = results.personality?.type || '';

    // Default defenses
    const strengthMisuse = {
      situation: 'Menggunakan kekuatan di situasi yang tidak tepat',
      signs: 'Rasa frustasi dan kelelahan yang tidak biasa',
      solution: 'Analisis konteks sebelum menerapkan pendekatan natural',
    };

    const developmentWall = {
      barrier: 'Kemacetan pengembangan akibat zona nyaman',
      trigger: 'Penghindaran tantangan baru',
      shortcut: 'Mentor atau coach untuk perspektif baru',
    };

    // Customize based on strength
    if (strength === 'Visionary Pioneers') {
      strengthMisuse.situation =
        'Terlalu fokus pada visi tanpa perencanaan implementasi';
      strengthMisuse.signs = 'Proyek yang selalu dimulai tapi jarang selesai';
      strengthMisuse.solution = 'Kolaborasi dengan implementation partner';
    } else if (strength === 'Insightful Truth-Seekers') {
      strengthMisuse.situation = 'Terlalu kritis tanpa menawarkan solusi';
      strengthMisuse.signs = 'Tim merasa diintimidasi oleh analisis Anda';
      strengthMisuse.solution =
        'Seimbangkan kritik dengan solusi dan appresiasi';
    } else if (strength === 'Inspiring Connectors') {
      strengthMisuse.situation = 'Over-promise untuk menyenangkan semua pihak';
      strengthMisuse.signs = 'Burnout dari terlalu banyak komitmen';
      strengthMisuse.solution = 'Tetapkan batasan yang jelas dan prioritas';
    }

    // Customize based on personality
    if (personality.includes('Connector')) {
      developmentWall.barrier = 'Kesulitan fokus pada satu bidang mendalam';
      developmentWall.trigger = 'Terlalu banyak proyek paralel';
      developmentWall.shortcut = 'Gunakan teknik Pomodoro dan task batching';
    } else if (personality.includes('Detail')) {
      developmentWall.barrier =
        'Analysis paralysis - terlalu banyak pertimbangan';
      developmentWall.trigger = 'Keputusan penting dengan data tidak lengkap';
      developmentWall.shortcut = 'Terapkan timeboxing untuk setiap analisis';
    }

    return {
      strengthMisuse,
      developmentWall,
    };
  }

  /**
   * Generate 1-Year Blueprint with timeline and major milestones
   */
  private generateYearlyBlueprint(results: any): any {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;

    // Generate milestones based on SHAPE profile
    const strengthMilestone = {
      phase: 'Penguatan Strength',
      project: this.getStrengthProject(results.strength?.primary || ''),
      startDate: `${currentYear}-07-01`,
      duration: '90d',
    };

    const heartMilestone = {
      phase: 'Eksplorasi Heart',
      project: this.getHeartProject(results.heart?.primary || ''),
      startDate: `${currentYear}-10-01`,
      duration: '60d',
    };

    const abilityMilestone = {
      phase: 'Amplifikasi Ability',
      project: this.getAbilityProject(results.abilities?.primary || ''),
      startDate: `${currentYear}-12-01`,
      duration: '45d',
    };

    const personalityMilestone = {
      phase: 'Optimasi Personality',
      project: this.getPersonalityProject(results.personality?.type || ''),
      startDate: `${nextYear}-02-01`,
      duration: '30d',
    };

    const experienceMilestone = {
      phase: 'Integrasi Experience',
      project: this.getExperienceProject(results.experience?.pattern || ''),
      startDate: `${nextYear}-03-15`,
      duration: '60d',
    };

    return {
      title: `Siklus SHAPE ${currentYear}-${nextYear}`,
      milestones: [
        strengthMilestone,
        heartMilestone,
        abilityMilestone,
        personalityMilestone,
        experienceMilestone,
      ],
      // For mermaid gantt chart in frontend
      ganttData: {
        dateFormat: 'YYYY-MM-DD',
        title: `Siklus SHAPE ${currentYear}-${nextYear}`,
        sections: [
          {
            title: 'Penguatan Strength',
            tasks: [
              {
                id: 'strength',
                name: strengthMilestone.project,
                start: strengthMilestone.startDate,
                duration: strengthMilestone.duration,
                status: 'active',
              },
            ],
          },
          {
            title: 'Eksplorasi Heart',
            tasks: [
              {
                id: 'heart',
                name: heartMilestone.project,
                start: heartMilestone.startDate,
                duration: heartMilestone.duration,
                status: 'crit',
              },
            ],
          },
          {
            title: 'Amplifikasi Ability',
            tasks: [
              {
                id: 'ability',
                name: abilityMilestone.project,
                start: abilityMilestone.startDate,
                duration: abilityMilestone.duration,
              },
            ],
          },
        ],
      },
      callToAction: {
        sevenDays: 'Lakukan pemetaan kekuatan dengan reflection journal',
        thirtyDays: 'Ikuti workshop sesuai rekomendasi strength',
        ninetyDays: 'Implementasikan proyek pertama dalam blueprint',
      },
    };
  }

  /**
   * Check if a specific assessment area is complete
   * @param results The assessment results object
   * @param area The area to check ('strength', 'heart', 'abilities', 'personality', 'experience')
   * @returns Boolean indicating if the area is complete
   */
  isAreaComplete(results: any, area: string): boolean {
    if (!results || typeof results !== 'object') {
      console.log(
        `isAreaComplete: No results or invalid object for area ${area}`
      );
      return false;
    }

    const validAreas = [
      'strength',
      'heart',
      'abilities',
      'personality',
      'experience',
    ];

    if (!validAreas.includes(area)) {
      console.log(
        `Invalid area: ${area}. Must be one of ${validAreas.join(', ')}`
      );
      return false;
    }

    const areaData = results[area];
    const isComplete = !!(areaData && Object.keys(areaData).length > 0);

    console.log(`isAreaComplete for ${area}:`, {
      areaData,
      hasData: !!areaData,
      keyCount: areaData ? Object.keys(areaData).length : 0,
      isComplete,
    });

    return isComplete;
  }

  /**
   * Get the completion status of all assessment areas
   * @param results The assessment results object
   * @returns Object with completion status for each area and overall completion
   */
  getAssessmentCompletionStatus(results: any): any {
    if (!results || typeof results !== 'object') {
      return {
        strength: false,
        heart: false,
        abilities: false,
        personality: false,
        experience: false,
        categoriesComplete: false,
        allComplete: false,
        completionPercentage: 0,
      };
    }

    const areas = [
      'strength',
      'heart',
      'abilities',
      'personality',
      'experience',
    ];
    const status: { [key: string]: boolean } = {};

    // Check each area
    areas.forEach((area) => {
      status[area] = this.isAreaComplete(results, area);
    });

    // Check categories
    const categoriesComplete = !!(
      results.categories &&
      Array.isArray(results.categories) &&
      results.categories.length > 0
    );

    // Calculate completion
    const completedAreas = Object.values(status).filter(Boolean).length;
    const completionPercentage = Math.round(
      (completedAreas / areas.length) * 100
    );

    // Check if all complete
    const allComplete = completedAreas === areas.length && categoriesComplete;

    return {
      ...status,
      categoriesComplete,
      allComplete,
      completionPercentage,
    };
  }

  /**
   * Generate Personal Archetype based on SHAPE combination
   */
  private generatePersonalArchetype(results: any): any {
    const strength = results.strength?.primary || '';
    const heart = results.heart?.primary || '';
    const personality = results.personality?.type || '';

    // Advanced archetype generation based on combinations
    const archetypes = {
      'Visionary Pioneers + Education + Visionary Driver': {
        name: 'Guru Visioner',
        description:
          'Pemimpin transformasi pendidikan yang membuka jalan baru dalam pembelajaran',
        metaphor: 'Mercusuar yang menerangi jalur pendidikan masa depan',
        signature_strength: 'Memadukan visi futuristik dengan passion mengajar',
      },
      'Inspiring Connectors + Business + Dynamic Connector': {
        name: 'Entrepreneur Sosial',
        description: 'Pembangun jembatan antara bisnis dan dampak sosial',
        metaphor: 'Sungai yang mengalirkan inovasi ke berbagai ekosistem',
        signature_strength:
          'Menghubungkan peluang bisnis dengan kebutuhan masyarakat',
      },
      'Supportive Nurturers + Family + Steady Supporter': {
        name: 'Penjaga Kebijaksanaan Komunal',
        description: 'Pemelihara nilai-nilai dan tradisi dalam komunitas',
        metaphor: 'Pohon besar yang memberikan naungan bagi generasi',
        signature_strength:
          'Menjaga keharmonisan dan pertumbuhan berkelanjutan',
      },
      'Insightful Truth-Seekers + Religion + Precision Analyst': {
        name: 'Pencari Makna Sejati',
        description: 'Penggali kebenaran spiritual dan filosofis',
        metaphor: 'Sumur dalam yang mengalirkan kebijaksanaan',
        signature_strength:
          'Mengungkap esensi spiritual dari kompleksitas kehidupan',
      },
      'Clarifying Mentors + Communication / Media + Dynamic Connector': {
        name: 'Storyteller Inspiratif',
        description:
          'Pencerita yang mengubah kompleksitas menjadi narasi yang bermakna',
        metaphor: 'Jembatan yang menghubungkan ide dengan hati audiens',
        signature_strength:
          'Menyederhanakan kompleksitas menjadi cerita yang menggerakkan',
      },
    };

    const archetypeKey = `${strength} + ${heart} + ${personality}`;
    const selectedArchetype = (archetypes as any)[archetypeKey] || {
      name: 'Catalyst Transformasi',
      description: 'Agen perubahan dengan kombinasi unik SHAPE',
      metaphor: 'Katalis yang mengubah potensi menjadi kenyataan',
      signature_strength:
        'Memadukan berbagai kekuatan untuk menciptakan dampak',
    };

    return {
      ...selectedArchetype,
      combination: `${strength} × ${heart} × ${personality}`,
      uniqueness_factor: this.calculateUniquenessScore(results),
    };
  }

  /**
   * Generate Executive Summary for Overall Report
   */
  private generateExecutiveSummary(results: any): any {
    const archetype = this.generatePersonalArchetype(results);
    const topCategories = this.getTopCategories(results.categories || [], 3);

    return {
      title: 'Ringkasan Eksekutif SHAPE',
      archetype_name: archetype.name,
      core_identity: archetype.description,
      metaphor: archetype.metaphor,
      top_strengths: topCategories,
      convergence_point: this.identifyConvergencePoint(results),
      next_steps: this.getNextSteps(results),
    };
  }

  /**
   * Generate Detailed Strength Report 💪
   * Enhanced with guidance from Strength.md
   */
  private generateDetailedStrengthReport(
    strengthData: any,
    allResults: any
  ): any {
    if (!strengthData) return null;

    const primary = strengthData.primary || 'Balanced Individual';
    const secondary = strengthData.secondary || '';
    const score = strengthData.score || 0;

    // Get detailed analysis from guidance (enhanced with Strength.md content)
    const strengthAnalysis = this.getEnhancedStrengthAnalysis(primary);
    const communicationStyle = this.getStrengthCommunicationStyle(primary);
    const developmentTips = this.getStrengthDevelopmentTips(
      primary,
      allResults
    );
    const teamRole = this.getStrengthTeamRole(primary);
    const powerPartners = this.getStrengthPowerPartners(primary);
    const famousFigures = this.getStrengthFamousFigures(primary);

    return {
      title: 'Analisis Kekuatan Mendalam',
      primary_strength: {
        name: primary,
        score: score,
        description: strengthAnalysis.description,
        core_traits: strengthAnalysis.traits,
        natural_talents: strengthAnalysis.talents,
        superpower: strengthAnalysis.superpower,
      },
      secondary_strength: secondary
        ? {
            name: secondary,
            synergy: this.analyzeStrengthSynergy(primary, secondary),
          }
        : null,
      unique_combination: this.generateStrengthCombination(primary, secondary),
      communication_style: communicationStyle,
      team_dynamics: teamRole,
      development_roadmap: developmentTips,
      application_areas: this.getStrengthApplications(primary, allResults),
      potential_challenges: this.getStrengthChallenges(primary),
      success_indicators: this.getStrengthSuccessIndicators(primary),
      power_partners: powerPartners,
      famous_inspirations: famousFigures,
      growth_opportunities: this.getStrengthGrowthOpportunities(primary),
    };
  }

  /**
   * Generate Detailed Heart Report ❤️
   * Enhanced with guidance from Heart.md
   */
  private generateDetailedHeartReport(heartData: any, allResults: any): any {
    if (!heartData) return null;

    const primary = heartData.primary || 'Personal Development';
    const score = heartData.score || 0;

    // Get enhanced heart analysis from guidance (Heart.md content)
    const heartAnalysis = this.getEnhancedHeartAnalysis(primary);
    const engagementRecommendations = this.getHeartEngagements(primary);
    const synergyPairs = this.identifyHeartSynergies(heartData, allResults);
    const creativeIdeas = this.getHeartCreativeIdeas(primary);
    const hiddenStrengths = this.getHeartHiddenStrengths(primary);
    const mythicalFigures = this.getHeartMythicalFigures(primary);

    return {
      title: 'Analisis Passion Mendalam',
      primary_passion: {
        name: primary,
        score: score,
        description: heartAnalysis.description,
        core_values: heartAnalysis.values,
        motivation_drivers: heartAnalysis.drivers,
        hidden_strengths: hiddenStrengths,
        love_language: heartAnalysis.love_language,
      },
      passion_profile: this.generatePassionProfile(primary),
      synergy_areas: synergyPairs,
      engagement_recommendations: engagementRecommendations,
      creative_ideas: creativeIdeas,
      passion_development: this.getPassionDevelopmentPath(primary, allResults),
      creative_expressions: this.getCreativeExpressions(primary),
      impact_opportunities: this.getImpactOpportunities(primary, allResults),
      passion_challenges: this.getPassionChallenges(primary),
      fulfillment_indicators: this.getPassionFulfillmentIndicators(primary),
      mythical_inspirations: mythicalFigures,
      warning_signs: this.getHeartWarnings(primary),
    };
  }

  /**
   * Generate Detailed Abilities Report 🧠
   * Enhanced with guidance from Abilities.md
   */
  private generateDetailedAbilitiesReport(
    abilitiesData: any,
    allResults: any
  ): any {
    if (!abilitiesData) return null;

    // Process 6 categories of abilities (updated with Abilities.md categories)
    const categories = [
      'Cognitive Skills',
      'Social Skills',
      'Technical Skills',
      'Creative Skills',
      'Leadership Skills',
      'Communication Skills',
    ];

    const categoryAnalysis = categories.map((category) => {
      const score = this.getCategoryScore(
        category,
        allResults.categories || []
      );
      return {
        name: category,
        score: score,
        level: this.getAbilityLevel(score),
        description: this.getAbilityDescription(category),
        strengths: this.getAbilityStrengths(category, score),
        development_areas: this.getAbilityDevelopmentAreas(category, score),
        improvement_plan: this.getAbilityImprovementPlan(category, score),
        hidden_strengths: this.getAbilityHiddenStrengths(category),
        love_language: this.getAbilityLoveLanguage(category),
        mythical_inspirations: this.getAbilityMythicalFigures(category),
        warning_signs: this.getAbilityWarnings(category),
        creative_ideas: this.getAbilityCreativeIdeas(category),
      };
    });

    const topAbilities = categoryAnalysis
      .filter((cat) => cat.score > 35)
      .sort((a, b) => b.score - a.score);
    const developmentAreas = categoryAnalysis.filter((cat) => cat.score < 26);

    return {
      title: 'Analisis Kemampuan Komprehensif',
      overview: {
        dominant_abilities: topAbilities.slice(0, 2),
        complementary_abilities: topAbilities.slice(2),
        development_priorities: developmentAreas,
      },
      category_breakdown: categoryAnalysis,
      ability_combinations: this.analyzeAbilityCombinations(topAbilities),
      skill_development_roadmap: this.createSkillRoadmap(
        categoryAnalysis,
        allResults
      ),
      learning_style: this.identifyLearningStyle(categoryAnalysis),
      performance_optimization: this.getPerformanceOptimization(
        topAbilities,
        allResults
      ),
      combination_profiles: this.getAbilityCombinationProfiles(topAbilities),
    };
  }

  /**
   * Generate Detailed Personality Report 👤
   */
  private generateDetailedPersonalityReport(
    personalityData: any,
    allResults: any
  ): any {
    if (!personalityData) return null;

    const type = personalityData.type || 'Balanced Individual';
    const score = personalityData.score || 0;

    const personalityAnalysis = this.getPersonalityAnalysis(type);
    const communicationStyle = this.getPersonalityCommunicationStyle(type);
    const teamDynamics = this.getPersonalityTeamDynamics(type);

    return {
      title: 'Analisis Kepribadian Mendalam',
      personality_type: {
        name: type,
        score: score,
        description: personalityAnalysis.description,
        core_traits: personalityAnalysis.traits,
        behavioral_patterns: personalityAnalysis.patterns,
      },
      communication_preferences: communicationStyle,
      team_dynamics: teamDynamics,
      leadership_style: this.getPersonalityLeadershipStyle(type),
      decision_making: this.getPersonalityDecisionStyle(type),
      stress_response: this.getPersonalityStressResponse(type),
      growth_areas: this.getPersonalityGrowthAreas(type),
      relationship_patterns: this.getPersonalityRelationshipPatterns(type),
      work_environment: this.getPersonalityWorkEnvironment(type),
      development_strategies: this.getPersonalityDevelopmentStrategies(
        type,
        allResults
      ),
    };
  }

  /**
   * Generate Detailed Experience Report 🌟
   */
  private generateDetailedExperienceReport(
    experienceData: any,
    allResults: any
  ): any {
    if (!experienceData) return null;

    const pattern = experienceData.pattern || 'Adaptive Learner';
    const insight = experienceData.insight || '';

    return {
      title: 'Analisis Pengalaman Mendalam',
      experience_pattern: {
        type: pattern,
        description: this.getExperiencePatternDescription(pattern),
        learning_style: this.getExperienceLearningStyle(pattern),
        growth_trajectory: this.getExperienceGrowthTrajectory(pattern),
      },
      core_competencies: this.extractCoreCompetencies(
        experienceData,
        allResults
      ),
      transformative_moments:
        this.identifyTransformativeMoments(experienceData),
      learning_patterns: this.analyzeLearningPatterns(experienceData),
      skill_evolution: this.mapSkillEvolution(experienceData, allResults),
      experience_integration: this.analyzeExperienceIntegration(
        experienceData,
        allResults
      ),
      future_blueprint: this.createFutureBlueprint(experienceData, allResults),
      wisdom_gained: this.extractWisdomGained(experienceData),
      mentorship_opportunities: this.identifyMentorshipOpportunities(
        experienceData,
        allResults
      ),
    };
  }

  // =============================================================================
  // DETAILED ANALYSIS HELPER METHODS
  // =============================================================================

  /**
   * Get detailed analysis for each strength type from guidance
   */
  private getStrengthAnalysis(strength: string): any {
    const analyses = {
      'Visionary Pioneers': {
        description: 'Kemampuan melihat peluang baru dan memulai terobosan',
        traits: [
          'Berorientasi masa depan',
          'Berani mengambil risiko',
          'Inovatif',
        ],
        talents: [
          'Identifikasi peluang',
          'Inisiasi proyek',
          'Transformasi organisasi',
        ],
      },
      'Insightful Truth-Seekers': {
        description:
          'Kemampuan menemukan inti kebenaran dan prinsip fundamental',
        traits: ['Analitis mendalam', 'Berprinsip kuat', 'Objektif'],
        talents: [
          'Analisis akar masalah',
          'Penjaga integritas',
          'Penelitian mendalam',
        ],
      },
      'Inspiring Connectors': {
        description: 'Kemampuan menghubungkan orang dengan ide dan peluang',
        traits: ['Komunikatif', 'Empati tinggi', 'Networker alami'],
        talents: [
          'Membangun hubungan',
          'Persuasi positif',
          'Fasilitasi kolaborasi',
        ],
      },
      'Supportive Nurturers': {
        description: 'Kemampuan mendukung pertumbuhan orang lain',
        traits: ['Sabar', 'Perhatian', 'Konsisten'],
        talents: [
          'Pembinaan personal',
          'Menciptakan lingkungan aman',
          'Mentoring',
        ],
      },
      'Clarifying Mentors': {
        description: 'Kemampuan menjelaskan konsep kompleks secara sistematis',
        traits: ['Sistematis', 'Teliti', 'Pedagogis'],
        talents: [
          'Penyederhanaan kompleksitas',
          'Pelatihan efektif',
          'Pengembangan kurikulum',
        ],
      },
    };

    return (
      (analyses as any)[strength] || {
        description: 'Kombinasi unik dari berbagai kekuatan',
        traits: ['Adaptif', 'Seimbang', 'Fleksibel'],
        talents: ['Multi-tasking', 'Problem solving', 'Adaptasi cepat'],
      }
    );
  }

  /**
   * Get communication style based on strength
   */
  private getStrengthCommunicationStyle(strength: string): any {
    const styles = {
      'Visionary Pioneers': {
        approach: 'Inspirational dan visioner',
        language: [
          'Bagaimana jika...',
          'Bayangkan ketika...',
          'Potensi besar di sini!',
        ],
        media_preference: 'Presentasi visual, peta konsep, sketsa visi',
        effectiveness: 'Terbaik saat memotivasi perubahan dan inovasi',
      },
      'Insightful Truth-Seekers': {
        approach: 'Analitis dan berbasis data',
        language: [
          'Prinsip dasarnya...',
          'Data menunjukkan...',
          'Akar masalahnya...',
        ],
        media_preference: 'Diagram sebab-akibat, analisis mendalam',
        effectiveness: 'Terbaik saat menjelaskan kompleksitas dan kebenaran',
      },
      'Inspiring Connectors': {
        approach: 'Relasional dan storytelling',
        language: [
          'Ceritamu mengingatkan...',
          'Mari kolaborasi!',
          'Kita semua ingin...',
        ],
        media_preference: 'Storytelling, testimoni, platform jaringan',
        effectiveness: 'Terbaik saat membangun hubungan dan engagement',
      },
      'Supportive Nurturers': {
        approach: 'Empati dan dukungan',
        language: [
          'Bagaimana perasaanmu...',
          'Aku di sini untukmu...',
          'Langkah kecil dulu...',
        ],
        media_preference: 'Percakapan personal, feedback konstruktif',
        effectiveness: 'Terbaik saat membimbing dan mengembangkan orang',
      },
      'Clarifying Mentors': {
        approach: 'Struktural dan edukatif',
        language: [
          'Mari kita bagi menjadi...',
          'Pertama, kedua, ketiga...',
          'Contohnya adalah...',
        ],
        media_preference: 'Modul pembelajaran, framework terstruktur',
        effectiveness: 'Terbaik saat mengajar dan menjelaskan konsep',
      },
    };

    return (
      (styles as any)[strength] || {
        approach: 'Adaptif sesuai situasi',
        language: [
          'Bagaimana menurutmu?',
          'Mari kita diskusi...',
          'Apa pendapatmu?',
        ],
        media_preference: 'Fleksibel sesuai kebutuhan',
        effectiveness: 'Efektif dalam berbagai konteks komunikasi',
      }
    );
  }

  /**
   * Get development tips for specific strength
   */
  private getStrengthDevelopmentTips(strength: string, allResults: any): any {
    const tips = {
      'Visionary Pioneers': {
        focus_areas: ['Eksekusi detail', 'Kesabaran proses', 'Risk assessment'],
        development_actions: [
          'Berpasangan dengan eksekutor detail',
          'Gunakan framework MVP untuk validasi',
          'Latih skill project management',
        ],
        timeline: '90 hari',
        success_metrics: [
          'Completion rate proyek',
          'Quality of execution',
          'Stakeholder satisfaction',
        ],
      },
      'Insightful Truth-Seekers': {
        focus_areas: [
          'Komunikasi yang lebih positif',
          'Solusi konstruktif',
          'Empati',
        ],
        development_actions: [
          'Sampaikan kritik dengan alternatif solusi',
          'Pelajari teknik feedforward',
          'Practice active listening',
        ],
        timeline: '60 hari',
        success_metrics: [
          'Feedback positivity ratio',
          'Solution contribution',
          'Team harmony',
        ],
      },
      'Inspiring Connectors': {
        focus_areas: ['Fokus dan prioritas', 'Saying no', 'Deep work'],
        development_actions: [
          'Time blocking untuk deep work',
          'Practice selective engagement',
          'Develop documentation habits',
        ],
        timeline: '45 hari',
        success_metrics: [
          'Focus time percentage',
          'Project completion',
          'Quality of connections',
        ],
      },
      'Supportive Nurturers': {
        focus_areas: [
          'Assertiveness',
          'Boundary setting',
          'Strategic thinking',
        ],
        development_actions: [
          'Practice saying no with alternatives',
          'Develop strategic planning skills',
          'Learn conflict resolution',
        ],
        timeline: '60 hari',
        success_metrics: [
          'Boundary maintenance',
          'Strategic contribution',
          'Personal wellbeing',
        ],
      },
      'Clarifying Mentors': {
        focus_areas: ['Emotional intelligence', 'Adaptability', 'Innovation'],
        development_actions: [
          'Study different learning styles',
          'Practice improvisational teaching',
          'Develop emotional awareness',
        ],
        timeline: '75 hari',
        success_metrics: [
          'Student engagement',
          'Learning effectiveness',
          'Adaptability score',
        ],
      },
    };

    return (
      (tips as any)[strength] || {
        focus_areas: ['Self-awareness', 'Skill diversification', 'Leadership'],
        development_actions: [
          'Regular self-reflection',
          'Cross-functional learning',
          'Leadership opportunities',
        ],
        timeline: '60 hari',
        success_metrics: [
          'Self-awareness level',
          'Skill breadth',
          'Leadership impact',
        ],
      }
    );
  }

  /**
   * Get heart analysis from guidance
   */
  private getHeartAnalysis(heart: string): any {
    const analyses = {
      'Art & Entertainment': {
        description: 'Memandang dunia sebagai palet emosi yang hidup',
        values: ['Keindahan', 'Ekspresi autentik', 'Transformasi emosional'],
        drivers: [
          'Kreativitas tanpa batas',
          'Apresiasi estetika',
          'Dampak emosional',
        ],
      },
      Business: {
        description: 'Melihat masalah sebagai model bisnis yang belum lahir',
        values: ['Efisiensi', 'Inovasi berkelanjutan', 'Value creation'],
        drivers: [
          'Pertumbuhan ekonomi',
          'Problem solving',
          'Systematic improvement',
        ],
      },
      'Communication / Media': {
        description:
          'Memandang informasi sebagai organisme hidup yang perlu dikawinkan',
        values: ['Transparansi', 'Koneksi makna', 'Akses informasi'],
        drivers: [
          'Menyambung realitas',
          'Storytelling impact',
          'Information democracy',
        ],
      },
      Family: {
        description: 'Melihat rumah sebagai museum hidup yang terus berevolusi',
        values: ['Harmoni generasi', 'Nilai tradisi', 'Ikatan emosional'],
        drivers: ['Legacy building', 'Relational depth', 'Generational wisdom'],
      },
      Religion: {
        description: 'Mencari harmoni antara keyakinan dan kehidupan modern',
        values: ['Spiritualitas', 'Makna hidup', 'Komunitas faith'],
        drivers: [
          'Transcendental purpose',
          'Moral guidance',
          'Community service',
        ],
      },
      Education: {
        description: 'Percaya pendidikan adalah kunci kemajuan masyarakat',
        values: [
          'Pertumbuhan intelektual',
          'Akses pembelajaran',
          'Transformasi generasi',
        ],
        drivers: [
          'Knowledge democratization',
          'Human development',
          'Social progress',
        ],
      },
      Government: {
        description: 'Memandang kebijakan sebagai alat transformasi masyarakat',
        values: ['Keadilan publik', 'Pelayanan masyarakat', 'Good governance'],
        drivers: [
          'Social justice',
          'Public welfare',
          'Institutional excellence',
        ],
      },
    };

    return (
      (analyses as any)[heart] || {
        description: 'Passion yang beragam dan adaptif',
        values: [
          'Fleksibilitas',
          'Pembelajaran berkelanjutan',
          'Kontribusi positif',
        ],
        drivers: [
          'Personal growth',
          'Meaningful contribution',
          'Balanced life',
        ],
      }
    );
  }

  /**
   * Get ability level interpretation
   */
  private getAbilityLevel(score: number): string {
    if (score >= 36) return 'Kemampuan Unggulan';
    if (score >= 26) return 'Kemampuan Menengah';
    return 'Kemampuan Dasar';
  }

  /**
   * Get ability description for each category
   */
  private getAbilityDescription(category: string): string {
    const descriptions = {
      'Cognitive Skills':
        'Kemampuan berpikir, analisis, dan pemecahan masalah kompleks',
      'Social Skills':
        'Kemampuan berinteraksi, berkomunikasi, dan bekerja sama dengan orang lain',
      'Technical Skills':
        'Kemampuan menggunakan alat, teknologi, dan keahlian teknis',
      'Management Skills':
        'Kemampuan mengatur, mengorganisir, dan memimpin orang dan proses',
      'Creative Skills':
        'Kemampuan berkreasi, berinovasi, dan mengekspresikan ide secara artistik',
      'Physical Skills': 'Kemampuan fisik, koordinasi, dan ketahanan tubuh',
    };

    return (
      (descriptions as any)[category] ||
      'Kemampuan khusus dalam bidang tertentu'
    );
  }

  /**
   * Get personality analysis
   */
  private getPersonalityAnalysis(type: string): any {
    const analyses = {
      'Visionary Driver': {
        description: 'Pemimpin yang berorientasi hasil dan visi masa depan',
        traits: ['Tegas', 'Berorientasi hasil', 'Pengambil risiko'],
        patterns: ['Inisiatif tinggi', 'Keputusan cepat', 'Target-oriented'],
      },
      'Dynamic Connector': {
        description: 'Komunikator yang energik dan persuasif',
        traits: ['Sosial', 'Optimis', 'Persuasif'],
        patterns: ['Networking aktif', 'Storytelling', 'Relationship building'],
      },
      'Steady Supporter': {
        description: 'Pendukung yang stabil dan dapat diandalkan',
        traits: ['Sabar', 'Kolaboratif', 'Stabil'],
        patterns: ['Kerja tim', 'Konsistensi', 'Dukungan emosional'],
      },
      'Precision Analyst': {
        description: 'Analis yang sistematis dan akurat',
        traits: ['Sistematis', 'Akurat', 'Analitis'],
        patterns: ['Detail-oriented', 'Data-driven', 'Process improvement'],
      },
    };

    return (
      (analyses as any)[type] || {
        description: 'Kepribadian yang seimbang dan adaptif',
        traits: ['Fleksibel', 'Seimbang', 'Adaptif'],
        patterns: [
          'Situational adaptation',
          'Balanced approach',
          'Contextual response',
        ],
      }
    );
  }

  /**
   * Generate strength combination analysis
   */
  private generateStrengthCombination(primary: string, secondary: string): any {
    if (!secondary) return null;

    const combinations = {
      'Visionary Pioneers + Inspiring Connectors': {
        name: 'Visionary Communicator',
        description:
          'Kemampuan melihat masa depan dan mengkomunikasikannya dengan inspiratif',
        synergy:
          'Visi futuristik diperkuat dengan kemampuan storytelling yang kuat',
        applications: [
          'Thought leadership',
          'Change management',
          'Innovation evangelism',
        ],
      },
      'Insightful Truth-Seekers + Clarifying Mentors': {
        name: 'Wisdom Teacher',
        description:
          'Kemampuan menemukan kebenaran dan menyampaikannya dengan jelas',
        synergy:
          'Analisis mendalam dikombinasikan dengan kemampuan pengajaran sistematis',
        applications: [
          'Research and education',
          'Consulting',
          'Knowledge management',
        ],
      },
      'Supportive Nurturers + Inspiring Connectors': {
        name: 'Community Builder',
        description: 'Kemampuan membangun dan memelihara komunitas yang kuat',
        synergy: 'Dukungan personal diperkuat dengan kemampuan networking',
        applications: [
          'Community management',
          'Team building',
          'Social initiatives',
        ],
      },
    };

    const key = `${primary} + ${secondary}`;
    return (
      (combinations as any)[key] || {
        name: 'Hybrid Strength',
        description: `Kombinasi unik antara ${primary} dan ${secondary}`,
        synergy: 'Sinergi kekuatan yang saling melengkapi',
        applications: [
          'Multi-domain leadership',
          'Cross-functional roles',
          'Innovative approaches',
        ],
      }
    );
  }

  /**
   * Extract core competencies from experience
   */
  private extractCoreCompetencies(
    experienceData: any,
    allResults: any
  ): string[] {
    // This would normally parse narrative data, for now return based on other indicators
    const competencies = [];

    if (allResults.strength?.primary === 'Visionary Pioneers') {
      competencies.push(
        'Strategic Planning',
        'Innovation Management',
        'Change Leadership'
      );
    }
    if (allResults.heart?.primary === 'Education') {
      competencies.push(
        'Curriculum Development',
        'Learning Design',
        'Knowledge Transfer'
      );
    }
    if (allResults.abilities?.primary === 'Cognitive Skills') {
      competencies.push(
        'Problem Solving',
        'Critical Thinking',
        'Research Methods'
      );
    }

    return competencies.length > 0
      ? competencies
      : ['Adaptability', 'Learning Agility', 'Cross-functional Collaboration'];
  }

  /**
   * Get category score from results
   */
  private getCategoryScore(categoryName: string, categories: any[]): number {
    const category = categories.find((cat) => cat.name === categoryName);
    return category ? category.score || 0 : 25; // Default to average
  }

  // =============================================================================
  // MISSING HELPER METHODS
  // =============================================================================

  private calculateUniquenessScore(results: any): number {
    // Calculate uniqueness based on combination of factors
    const factors = [
      results.strength?.primary,
      results.heart?.primary,
      results.abilities?.primary,
      results.personality?.type,
      results.experience?.pattern,
    ].filter(Boolean);

    return Math.min(95, 60 + factors.length * 7); // Base 60% + 7% per factor
  }

  private getTopCategories(categories: any[], limit: number = 3): string[] {
    return categories
      .sort((a, b) => (b.score || 0) - (a.score || 0))
      .slice(0, limit)
      .map((cat) => cat.name || 'Unknown');
  }

  private identifyConvergencePoint(results: any): string {
    const strength = results.strength?.primary || '';
    const heart = results.heart?.primary || '';

    if (strength.includes('Visionary') && heart === 'Education') {
      return 'Inovasi Pendidikan Transformatif';
    }
    if (strength.includes('Connector') && heart === 'Business') {
      return 'Ekosistem Bisnis Berkelanjutan';
    }
    if (strength.includes('Nurturer') && heart === 'Family') {
      return 'Pengembangan Komunitas Harmonis';
    }

    return 'Integrasi Multi-Dimensi SHAPE';
  }

  private getNextSteps(results: any): string[] {
    return [
      `Fokuskan pada pengembangan ${
        results.strength?.primary || 'kekuatan utama'
      }`,
      `Eksplorasi lebih dalam passion di ${
        results.heart?.primary || 'area minat'
      }`,
      `Tingkatkan kemampuan ${
        results.abilities?.primary || 'keterampilan dominan'
      }`,
      'Implementasikan insight dari analisis kepribadian',
      'Bangun berdasarkan pola pengalaman yang telah terbentuk',
    ];
  }

  // Additional missing methods with basic implementations
  private analyzeStrengthSynergy(primary: string, secondary: string): string {
    return `Sinergi antara ${primary} dan ${secondary} menciptakan keseimbangan antara visi dan eksekusi`;
  }

  private getStrengthTeamRole(strength: string): any {
    const roles: { [key: string]: any } = {
      'Visionary Pioneers': {
        primary_role: 'Initiator dan Strategic Planner',
        team_contribution:
          'Membawa perspektif masa depan dan memulai proyek baru',
        collaboration_style: 'Menginspirasi dengan visi, mendorong inovasi',
      },
      'Insightful Truth-Seekers': {
        primary_role: 'Analyst dan Quality Controller',
        team_contribution:
          'Memastikan keputusan berdasarkan data dan prinsip yang benar',
        collaboration_style:
          'Bertanya mendalam, memberikan perspektif objektif',
      },
      'Inspiring Connectors': {
        primary_role: 'Facilitator dan Community Builder',
        team_contribution: 'Membangun hubungan dan memfasilitasi kolaborasi',
        collaboration_style: 'Interaktif dan energik, fokus pada hubungan',
      },
      'Supportive Nurturers': {
        primary_role: 'Mentor dan Team Developer',
        team_contribution:
          'Mendukung pertumbuhan dan kesejahteraan anggota tim',
        collaboration_style: 'Konsensual dan kolaboratif, membangun harmoni',
      },
      'Clarifying Mentors': {
        primary_role: 'Trainer dan Knowledge Manager',
        team_contribution:
          'Menyampaikan pengetahuan dan mengembangkan keterampilan',
        collaboration_style: 'Struktural dan edukatif, fokus pada pengajaran',
      },
    };

    return (
      roles[strength] || {
        primary_role: 'Contributor',
        team_contribution: 'Memberikan kontribusi berdasarkan kekuatan unik',
        collaboration_style: 'Adaptif sesuai kebutuhan tim',
      }
    );
  }

  private getStrengthApplications(strength: string, allResults: any): string[] {
    const applications: { [key: string]: string[] } = {
      'Visionary Pioneers': [
        'Strategic Planning',
        'Innovation Management',
        'Change Leadership',
      ],
      'Insightful Truth-Seekers': [
        'Research & Analysis',
        'Quality Assurance',
        'Ethics Advisory',
      ],
      'Inspiring Connectors': [
        'Relationship Building',
        'Partnership Development',
        'Community Building',
      ],
      'Supportive Nurturers': [
        'Team Development',
        'Mentoring Programs',
        'Organizational Culture',
      ],
      'Clarifying Mentors': [
        'Training & Development',
        'Knowledge Management',
        'Process Documentation',
      ],
    };

    return (
      applications[strength] || [
        'General Leadership',
        'Problem Solving',
        'Team Collaboration',
      ]
    );
  }

  private getStrengthChallenges(strength: string): string[] {
    const challenges: { [key: string]: string[] } = {
      'Visionary Pioneers': [
        'Impatience dengan detail',
        'Over-commitment pada banyak proyek',
        'Kesulitan dengan rutinitas',
      ],
      'Insightful Truth-Seekers': [
        'Dianggap terlalu kritis',
        'Kesulitan berkompromi',
        'Fokus berlebihan pada masalah',
      ],
      'Inspiring Connectors': [
        'Kesulitan saying no',
        'Over-promising',
        'Kelelahan sosial',
      ],
      'Supportive Nurturers': [
        'Kesulitan dengan konfrontasi',
        'Mengabaikan kebutuhan sendiri',
        'Terlalu protective',
      ],
      'Clarifying Mentors': [
        'Kekakuan dalam pendekatan',
        'Kesulitan dengan ambiguitas',
        'Terlalu detail-oriented',
      ],
    };

    return (
      challenges[strength] || [
        'Perlu pengembangan self-awareness',
        'Adaptabilitas situasional',
      ]
    );
  }

  private getStrengthSuccessIndicators(strength: string): string[] {
    const indicators: { [key: string]: string[] } = {
      'Visionary Pioneers': [
        'Jumlah proyek inovatif yang diluncurkan',
        'Tingkat adopsi ide baru',
        'Impact transformasi organisasi',
      ],
      'Insightful Truth-Seekers': [
        'Akurasi analisis dan prediksi',
        'Kualitas keputusan tim',
        'Tingkat kepercayaan stakeholder',
      ],
      'Inspiring Connectors': [
        'Ukuran dan kualitas network',
        'Tingkat kolaborasi tim',
        'Engagement level audience',
      ],
      'Supportive Nurturers': [
        'Growth rate team members',
        'Retention rate',
        'Satisfaction survey results',
      ],
      'Clarifying Mentors': [
        'Learning effectiveness metrics',
        'Knowledge transfer success',
        'Skill development outcomes',
      ],
    };

    return (
      indicators[strength] || [
        'Overall contribution to team goals',
        'Personal development progress',
      ]
    );
  }

  private generatePassionProfile(heart: string): string {
    const profiles: { [key: string]: string } = {
      'Art & Entertainment': 'Kreator Estetika',
      Business: 'Arsitek Peluang',
      'Communication / Media': 'Jembatan Narasi',
      Family: 'Pengrajin Harmoni',
      Religion: 'Pencari Transendensi',
      Education: 'Katalis Pembelajaran',
      Government: 'Agen Transformasi Publik',
    };

    return profiles[heart] || 'Explorer Multi-Domain';
  }

  private identifyHeartSynergies(heartData: any, allResults: any): any[] {
    // Basic implementation - would be more sophisticated in real application
    return [
      {
        area1: heartData.primary || 'Primary Interest',
        area2: allResults.strength?.primary || 'Main Strength',
        synergy_description: 'Kombinasi passion dan kekuatan alami',
        potential_applications: [
          'Leadership role',
          'Specialized expertise',
          'Innovation projects',
        ],
      },
    ];
  }

  private getHeartEngagements(heart: string): string[] {
    const engagements: { [key: string]: string[] } = {
      'Art & Entertainment': [
        'Gallery visits',
        'Creative workshops',
        'Performance arts',
        'Cultural events',
      ],
      Business: [
        'Startup communities',
        'Business networking',
        'Innovation hubs',
        'Entrepreneurship programs',
      ],
      'Communication / Media': [
        'Media production',
        'Content creation',
        'Public speaking',
        'Journalism workshops',
      ],
      Family: [
        'Family counseling',
        'Community building',
        'Parenting programs',
        'Relationship workshops',
      ],
      Religion: [
        'Spiritual retreats',
        'Faith communities',
        'Meditation practices',
        'Religious studies',
      ],
      Education: [
        'Teaching opportunities',
        'Curriculum development',
        'Educational technology',
        'Learning research',
      ],
      Government: [
        'Public service',
        'Policy development',
        'Civic engagement',
        'Community leadership',
      ],
    };

    return (
      engagements[heart] || [
        'Personal development',
        'Community involvement',
        'Learning opportunities',
      ]
    );
  }

  private getPassionDevelopmentPath(heart: string, allResults: any): any {
    return {
      short_term: `Explore ${heart} through hands-on experiences`,
      medium_term: `Develop expertise in specific ${heart} areas`,
      long_term: `Become a thought leader or change agent in ${heart}`,
      resources: this.getHeartEngagements(heart),
      success_metrics: [
        `Depth of knowledge in ${heart}`,
        'Impact on others',
        'Personal fulfillment',
      ],
    };
  }

  private getCreativeExpressions(heart: string): string[] {
    return [
      'Innovative approaches',
      'Unique perspectives',
      'Creative problem solving',
      'Artistic interpretations',
    ];
  }

  private getImpactOpportunities(heart: string, allResults: any): string[] {
    return [
      `${heart} advocacy`,
      'Community leadership',
      'Mentoring others',
      'Creating solutions',
    ];
  }

  private getPassionChallenges(heart: string): string[] {
    return [
      'Balancing passion with practical needs',
      'Avoiding burnout',
      'Maintaining objectivity',
    ];
  }

  private getPassionFulfillmentIndicators(heart: string): string[] {
    return [
      'Energy level when engaged',
      'Sense of purpose',
      'Flow state frequency',
      'Impact on others',
    ];
  }

  private getPersonalityTeamDynamics(type: string): any {
    const dynamics: { [key: string]: any } = {
      'Visionary Driver': {
        team_role: 'Leader/Initiator',
        contribution: 'Strategic direction and decision making',
        collaboration_preference: 'Goal-oriented discussions',
        conflict_style: 'Direct and solution-focused',
      },
      'Dynamic Connector': {
        team_role: 'Facilitator/Networker',
        contribution: 'Relationship building and communication',
        collaboration_preference: 'Interactive and energetic sessions',
        conflict_style: 'Diplomatic and people-focused',
      },
      'Steady Supporter': {
        team_role: 'Stabilizer/Helper',
        contribution: 'Consistency and emotional support',
        collaboration_preference: 'Harmonious and inclusive environment',
        conflict_style: 'Mediating and consensus-building',
      },
      'Precision Analyst': {
        team_role: 'Analyst/Quality Controller',
        contribution: 'Accuracy and systematic thinking',
        collaboration_preference: 'Structured and data-driven discussions',
        conflict_style: 'Fact-based and logical approach',
      },
    };

    return (
      dynamics[type] || {
        team_role: 'Adaptive contributor',
        contribution: 'Flexible support based on needs',
        collaboration_preference: 'Situational adaptation',
        conflict_style: 'Balanced and contextual',
      }
    );
  }

  // Placeholder methods for personality analysis
  private getPersonalityLeadershipStyle(type: string): any {
    return {
      style: `${type} Leadership`,
      description: 'Leadership approach based on personality type',
    };
  }

  private getPersonalityDecisionStyle(type: string): any {
    return {
      style: `${type} Decision Making`,
      description: 'Decision making approach based on personality',
    };
  }

  private getPersonalityStressResponse(type: string): any {
    return {
      response: `${type} Stress Response`,
      description: 'How personality type responds to stress',
    };
  }

  private getPersonalityGrowthAreas(type: string): string[] {
    return [
      'Self-awareness development',
      'Adaptability enhancement',
      'Communication improvement',
    ];
  }

  private getPersonalityRelationshipPatterns(type: string): any {
    return {
      patterns: `${type} Relationship Style`,
      description: 'Relationship patterns based on personality',
    };
  }

  private getPersonalityWorkEnvironment(type: string): any {
    return {
      environment: `${type} Work Preferences`,
      description: 'Ideal work environment for personality type',
    };
  }

  private getPersonalityDevelopmentStrategies(
    type: string,
    allResults: any
  ): any {
    return {
      strategies: [
        'Leverage personality strengths',
        'Address development areas',
        'Adapt to different contexts',
      ],
      timeline: '90 days',
      success_metrics: [
        'Self-awareness increase',
        'Effectiveness improvement',
        'Relationship quality',
      ],
    };
  }

  // Placeholder methods for experience analysis
  private getExperiencePatternDescription(pattern: string): string {
    return `Pattern description for ${pattern}`;
  }

  private getExperienceLearningStyle(pattern: string): string {
    return `Learning style associated with ${pattern}`;
  }

  private getExperienceGrowthTrajectory(pattern: string): string {
    return `Growth trajectory for ${pattern}`;
  }

  private identifyTransformativeMoments(experienceData: any): any[] {
    return [
      {
        moment: 'Career transition',
        impact: 'Significant personal growth',
        year: '2023',
      },
    ];
  }

  private analyzeLearningPatterns(experienceData: any): any {
    return {
      pattern: 'Continuous learning',
      description: 'Consistent pattern of skill development',
    };
  }

  private mapSkillEvolution(experienceData: any, allResults: any): any {
    return {
      evolution: 'Progressive skill development',
      timeline: 'Over career span',
    };
  }

  private analyzeExperienceIntegration(
    experienceData: any,
    allResults: any
  ): any {
    return {
      integration: 'Experience aligns with SHAPE profile',
      synergy: 'High',
    };
  }

  private createFutureBlueprint(experienceData: any, allResults: any): any {
    return {
      blueprint: 'Future development plan based on experience pattern',
      focus_areas: [
        'Skill enhancement',
        'Leadership development',
        'Impact expansion',
      ],
      timeline: '1-3 years',
    };
  }

  private extractWisdomGained(experienceData: any): string[] {
    return [
      'Adaptability is key',
      'Continuous learning essential',
      'Relationships matter',
    ];
  }

  private identifyMentorshipOpportunities(
    experienceData: any,
    allResults: any
  ): string[] {
    return [
      'Junior staff development',
      'Cross-functional training',
      'Knowledge sharing initiatives',
    ];
  }

  // Additional helper methods for abilities analysis
  private getAbilityStrengths(category: string, score: number): string[] {
    if (score > 35) {
      return [
        `Excellent ${category.toLowerCase()}`,
        'Natural talent',
        'Leadership potential',
      ];
    }
    return [`Competent ${category.toLowerCase()}`, 'Room for growth'];
  }

  private getAbilityDevelopmentAreas(
    category: string,
    score: number
  ): string[] {
    if (score < 26) {
      return [
        `Foundational ${category.toLowerCase()}`,
        'Structured learning needed',
      ];
    }
    return [
      `Advanced ${category.toLowerCase()}`,
      'Specialization opportunities',
    ];
  }

  private getAbilityImprovementPlan(category: string, score: number): any {
    return {
      plan: `Improve ${category}`,
      actions: [
        'Targeted practice',
        'Skill building exercises',
        'Real-world application',
      ],
      timeline: '60-90 days',
    };
  }

  private analyzeAbilityCombinations(abilities: any[]): any {
    return {
      combination: 'Synergistic ability mix',
      strengths: abilities.map((a) => a.name),
      applications: ['Multi-disciplinary roles', 'Complex problem solving'],
    };
  }

  private createSkillRoadmap(categories: any[], allResults: any): any {
    return {
      roadmap: 'Skill development pathway',
      phases: ['Foundation', 'Development', 'Mastery'],
      timeline: '6-12 months',
    };
  }

  private identifyLearningStyle(categories: any[]): any {
    return {
      style: 'Adaptive learning approach',
      preferences: [
        'Hands-on practice',
        'Theoretical understanding',
        'Social learning',
      ],
      effectiveness: 'High',
    };
  }

  private getPerformanceOptimization(abilities: any[], allResults: any): any {
    return {
      optimization: 'Performance enhancement strategies',
      focus_areas: abilities.map((a) => a.name),
      expected_outcomes: [
        'Improved efficiency',
        'Better results',
        'Higher satisfaction',
      ],
    };
  }

  private getPersonalityCommunicationStyle(type: string): any {
    const styles: { [key: string]: any } = {
      'Visionary Driver': {
        style: 'Direct and decisive',
        approach: 'Bottom-line focused, clear directives',
        best_for: 'Decision making and strategic discussions',
        avoid: 'Too much detail or emotional appeals',
      },
      'Dynamic Connector': {
        style: 'Enthusiastic and engaging',
        approach: 'Storytelling, relationship-focused',
        best_for: 'Presentations and team building',
        avoid: 'Dry facts or impersonal communication',
      },
      'Steady Supporter': {
        style: 'Patient and supportive',
        approach: 'Collaborative, consensus-building',
        best_for: 'Team harmony and conflict resolution',
        avoid: 'High pressure or confrontational approaches',
      },
      'Precision Analyst': {
        style: 'Detailed and systematic',
        approach: 'Data-driven, logical progression',
        best_for: 'Technical discussions and problem-solving',
        avoid: 'Rushed decisions or incomplete information',
      },
    };

    return (
      styles[type] || {
        style: 'Adaptive communication',
        approach: 'Flexible based on situation',
        best_for: 'Various contexts',
        avoid: 'One-size-fits-all approaches',
      }
    );
  }

  // ===== ENHANCED GUIDANCE INTEGRATION METHODS =====
  // These methods integrate content from the guidance markdown files

  /**
   * Enhanced Strength Analysis based on Strength.md guidance
   */
  private getEnhancedStrengthAnalysis(strength: string): any {
    const strengthGuidance: { [key: string]: any } = {
      'Visionary Pioneers': {
        description: 'Kemampuan melihat peluang baru dan memulai terobosan',
        traits: [
          'Pola pikir futuristik',
          'Berani mengambil risiko terukur',
          'Tidak nyaman dengan rutinitas',
        ],
        talents: [
          'Membuka pasar/jalur baru',
          'Mengenali peluang sebelum orang lain',
          'Memobilisasi tim menuju perubahan',
        ],
        superpower:
          'You naturally see possibilities where others see dead ends',
      },
      'Insightful Truth-Seekers': {
        description:
          'Kemampuan menemukan inti kebenaran dan prinsip fundamental',
        traits: [
          'Kepekaan tinggi pada ketidaksesuaian',
          'Dorongan kuat untuk integritas',
          'Analisis akar masalah yang tajam',
        ],
        talents: [
          'Mencegah tim dari keputusan gegabah',
          'Membongkar bias tersembunyi',
          'Penjaga standar etika/kualitas',
        ],
        superpower: 'You see through surface issues to find the root truth',
      },
      'Inspiring Connectors': {
        description: 'Kemampuan menghubungkan orang dengan ide dan peluang',
        traits: [
          'Kemampuan membaca kebutuhan audiens',
          'Energi tinggi dalam interaksi sosial',
          'Ahli menemukan kesamaan dari perbedaan',
        ],
        talents: [
          'Membentuk aliansi strategis',
          'Meningkatkan engagement stakeholder',
          'Menerjemahkan ide kompleks ke bahasa awam',
        ],
        superpower: 'You build bridges between people and possibilities',
      },
      'Supportive Nurturers': {
        description: 'Kemampuan mendukung pertumbuhan orang lain',
        traits: [
          'Radar tinggi untuk kebutuhan emosional',
          'Kesabaran dalam proses perkembangan',
          'Fokus pada kesejahteraan jangka panjang',
        ],
        talents: [
          'Menciptakan lingkungan psikologis aman',
          'Mempertahankan talenta kunci',
          'Mendeteksi masalah dini melalui observasi',
        ],
        superpower: 'You create safe spaces where people can grow and thrive',
      },
      'Clarifying Mentors': {
        description: 'Kemampuan menjelaskan konsep kompleks secara sistematis',
        traits: [
          'Bakat menyusun pengetahuan sistematis',
          'Kesabaran dalam repetisi pengajaran',
          'Fokus pada keterpahaman bukan kesepakatan',
        ],
        talents: [
          'Mempercepat onboarding',
          'Mencegah kesalahan berulang',
          'Menstandarisasi keunggulan',
        ],
        superpower: 'You transform complexity into clarity and understanding',
      },
    };

    return (
      strengthGuidance[strength] || {
        description: 'Kombinasi unik dari berbagai kekuatan',
        traits: ['Adaptable', 'Balanced', 'Versatile'],
        talents: [
          'Multi-faceted problem solving',
          'Cross-functional collaboration',
        ],
        superpower:
          'You bring a unique blend of capabilities to every situation',
      }
    );
  }

  /**
   * Get Power Partners for Strength based on guidance
   */
  private getStrengthPowerPartners(strength: string): string[] {
    const partners: { [key: string]: string[] } = {
      'Visionary Pioneers': [
        'Detail-Oriented Implementers',
        'Risk Assessment Specialists',
        'Process Managers',
      ],
      'Insightful Truth-Seekers': [
        'Creative Problem Solvers',
        'Implementation Experts',
        'Communication Specialists',
      ],
      'Inspiring Connectors': [
        'Technical Specialists',
        'Data Analysts',
        'Quality Controllers',
      ],
      'Supportive Nurturers': [
        'Strategic Planners',
        'Performance Managers',
        'Innovation Leaders',
      ],
      'Clarifying Mentors': [
        'Creative Innovators',
        'Relationship Builders',
        'Change Agents',
      ],
    };

    return (
      partners[strength] || [
        'Complementary Skill Sets',
        'Different Perspectives',
        'Diverse Backgrounds',
      ]
    );
  }

  /**
   * Get Famous Figures for Strength inspiration
   */
  private getStrengthFamousFigures(strength: string): string[] {
    const figures: { [key: string]: string[] } = {
      'Visionary Pioneers': ['Elon Musk', 'Steve Jobs', 'Richard Branson'],
      'Insightful Truth-Seekers': [
        'Warren Buffett',
        'Marie Curie',
        'Nelson Mandela',
      ],
      'Inspiring Connectors': ['Oprah Winfrey', 'Tony Robbins', 'Barack Obama'],
      'Supportive Nurturers': ['Mother Teresa', 'Fred Rogers', 'Maya Angelou'],
      'Clarifying Mentors': [
        'Albert Einstein',
        'Richard Feynman',
        'Bill Gates',
      ],
    };

    return figures[strength] || ['Leonardo da Vinci', 'Gandhi', 'Marie Curie'];
  }

  /**
   * Get Growth Opportunities for Strength
   */
  private getStrengthGrowthOpportunities(strength: string): any {
    const opportunities: { [key: string]: any } = {
      'Visionary Pioneers': {
        watch_out_for: 'Mengabaikan detail implementasi',
        develop: 'Partnership dengan eksekutor detail dan risk assessment',
        action: 'Gunakan framework MVP dan stage-gate evaluation',
      },
      'Insightful Truth-Seekers': {
        watch_out_for: 'Dianggap terlalu kritis/negatif',
        develop: 'Teknik feedforward dan creative problem-solving',
        action: 'Sampaikan kritik dengan solusi alternatif',
      },
      'Inspiring Connectors': {
        watch_out_for: 'Over-promise untuk menyenangkan orang',
        develop: 'Conflict resolution dan boundary setting',
        action: 'Kombinasikan cerita dengan data pendukung',
      },
      'Supportive Nurturers': {
        watch_out_for: 'Kesulitan membuat keputusan tegas',
        develop: 'Tough love skills dan self-care routines',
        action: 'Gunakan metrics untuk mengukur perkembangan',
      },
      'Clarifying Mentors': {
        watch_out_for: 'Kaku pada metode yang sudah bekerja',
        develop: 'Microlearning principles dan innovation methodologies',
        action: 'Kolaborasi dengan Connectors untuk engagement',
      },
    };

    return (
      opportunities[strength] || {
        watch_out_for: 'Overextending capabilities',
        develop: 'Continuous learning and adaptation',
        action: 'Seek feedback and iterate regularly',
      }
    );
  }

  /**
   * Enhanced Heart Analysis based on Heart.md guidance
   */
  private getEnhancedHeartAnalysis(heart: string): any {
    const heartGuidance: { [key: string]: any } = {
      'Art & Entertainment': {
        description: 'Memandang dunia sebagai palet emosi yang hidup',
        values: ['Keindahan', 'Ekspresi kreatif', 'Transformasi emosional'],
        drivers: [
          'Mengubah rutinitas menjadi pertunjukan',
          'Mengkomunikasikan yang tak terucap',
        ],
        love_language: [
          'Waktu tanpa batas untuk kreativitas',
          'Apresiasi tanpa syarat',
          'Kebebasan bereksperimen',
        ],
      },
      Business: {
        description: 'Melihat masalah sebagai model bisnis yang belum lahir',
        values: ['Efisiensi', 'Inovasi', 'Dampak terukur'],
        drivers: [
          'Mengubah sumber daya terbatas menjadi ekosistem berlimpah',
          'Menciptakan nilai berkelanjutan',
        ],
        love_language: [
          'Bukti nyata dampak',
          'Percobaan terkendali',
          'Jaringan tak terduga',
        ],
      },
      'Communication / Media': {
        description:
          'Memandang keheningan sebagai ruang kosong untuk diisi makna',
        values: ['Transparansi', 'Koneksi', 'Pengaruh positif'],
        drivers: [
          'Mengubah kebisingan menjadi simfoni',
          'Menyulam realitas melalui narasi',
        ],
        love_language: [
          'Perhatian tanpa batas',
          'Medium eksperimental',
          'Panggung tak terduga',
        ],
      },
      Family: {
        description: 'Melihat rumah sebagai museum hidup yang terus berevolusi',
        values: ['Kebersamaan', 'Warisan', 'Pertumbuhan bersama'],
        drivers: [
          'Mengubah konflik menjadi bahan bakar pertumbuhan',
          'Menciptakan ritual bermakna',
        ],
        love_language: [
          'Waktu berkualitas tanpa syarat',
          'Warisan hidup',
          'Ruangan aman',
        ],
      },
      Religion: {
        description: 'Memandang yang sakral dalam yang sehari-hari',
        values: ['Spiritualitas', 'Makna hidup', 'Transendensi'],
        drivers: [
          'Mengubah keraguan menjadi bahan bakar iman',
          'Menjembatani dimensi fisik dan metafisik',
        ],
        love_language: ['Keheningan aktif', 'Simbol hidup', 'Pertanyaan suci'],
      },
      Education: {
        description: 'Memandang ketidaktahuan sebagai lahan subur',
        values: ['Pengetahuan', 'Pertumbuhan', 'Pemberdayaan'],
        drivers: [
          'Mengubah kebosanan menjadi keingintahuan',
          'Menyambung titik tak terlihat',
        ],
        love_language: [
          'Pertanyaan bagus',
          'Laboratorium hidup',
          'Pembelajaran resiprokal',
        ],
      },
      Government: {
        description: 'Memandang kebijakan sebagai arsitektur sosial',
        values: ['Keadilan', 'Pelayanan', 'Perubahan sistemik'],
        drivers: [
          'Mengubah birokrasi menjadi koreografi sosial',
          'Mendengar detak jantung kolektif',
        ],
        love_language: [
          'Akses transparan',
          'Percobaan mikro',
          'Dialog tanpa topeng',
        ],
      },
    };

    return (
      heartGuidance[heart] || {
        description: 'Passion yang unik dan personal',
        values: ['Autentisitas', 'Pertumbuhan', 'Kontribusi'],
        drivers: ['Mengikuti panggilan hati', 'Menciptakan makna'],
        love_language: ['Pengakuan', 'Dukungan', 'Kebebasan berekspresi'],
      }
    );
  }

  /**
   * Get Creative Ideas for Heart based on guidance
   */
  private getHeartCreativeIdeas(heart: string): string[] {
    const ideas: { [key: string]: string[] } = {
      'Art & Entertainment': [
        'Proyek Seni Urban Guerilla',
        'Pertunjukan Realitas Campuran',
        'Kuliner Emosional',
      ],
      Business: ['Perpustakaan Alat', 'Pasar Mimpi', 'Bisnis Regeneratif'],
      'Communication / Media': [
        'Surat Masa Depan',
        'Podcast dalam Gelap',
        'Media Tanpa Kata',
      ],
      Family: [
        'Pertukaran Keluarga',
        'Kapsul Waktu Emosional',
        'Ritual Kesalahan',
      ],
      Religion: [
        'Wisata Spiritual Lintas Iman',
        'Teknologi Kontemplatif',
        'Kelas Keraguan Sistematis',
      ],
      Education: [
        'Sekolah Keterbalikan',
        'Pembelajaran Sensori Tunggal',
        'Museum Kegagalan',
      ],
      Government: [
        'Demokrasi Sensori',
        'Pemerintahan Bayangan Anak',
        'Kebijakan Fiksi Ilmiah',
      ],
    };

    return (
      ideas[heart] || [
        'Proyek Inovatif',
        'Eksperimen Kreatif',
        'Kolaborasi Lintas Bidang',
      ]
    );
  }

  /**
   * Get Hidden Strengths for Heart
   */
  private getHeartHiddenStrengths(heart: string): string {
    const strengths: { [key: string]: string } = {
      'Art & Entertainment':
        'Terapi kejut estetika - menggunakan keindahan tak terduga untuk memutus spiral pikiran negatif',
      Business:
        'Virus kemakmuran - sistem yang menyebarkan kesejahteraan melalui interaksi alami',
      'Communication / Media':
        'Menyulam realitas - menyambung fragmen informasi menjadi narasi kohesif',
      Family:
        'Regenerasi akar - memulihkan hubungan yang rusak melalui ingatan kolektif',
      Religion:
        'Penglihatan lapis - memahami realitas fisik dan metafisik secara simultan',
      Education:
        'Penyambung titik tak terlihat - menemukan pola dalam pengetahuan yang tampak acak',
      Government:
        'Mendengar detak jantung kolektif - merasakan denyut nadi masyarakat',
    };

    return (
      strengths[heart] || 'Kemampuan unik untuk menciptakan dampak positif'
    );
  }

  /**
   * Get Mythical Figures for Heart inspiration
   */
  private getHeartMythicalFigures(heart: string): string[] {
    const figures: { [key: string]: string[] } = {
      'Art & Entertainment': [
        "Puck (Midsummer Night's Dream)",
        'Baron Munchausen',
        'Scheherazade',
      ],
      Business: ['Midas (versi positif)', 'Hermes', 'Sisyphus yang Bahagia'],
      'Communication / Media': [
        'Echo (mitologi Yunani)',
        'Rasul John',
        'Anansi',
      ],
      Family: ['Hestia', 'Yggdrasil', 'Phoenix'],
      Religion: ['Tiresias', 'Lilith', 'Bodhisattva'],
      Education: ['Prometheus', 'Sokrates', 'Thoth'],
      Government: [
        "Plato's Philosopher King",
        'Lao Tzu',
        'Anansi sebagai Penipu',
      ],
    };

    return (
      figures[heart] || ['Wise Sage', 'Creative Muse', 'Transformative Leader']
    );
  }

  /**
   * Get Warning Signs for Heart
   */
  private getHeartWarnings(heart: string): string {
    const warnings: { [key: string]: string } = {
      'Art & Entertainment':
        'Keracunan Keindahan - kehilangan kemampuan melihat nilai dalam yang biasa-biasa saja',
      Business:
        'Logika Kalkulator - mengukur segala sesuatu termasuk yang tak seharusnya terukur',
      'Communication / Media':
        'Banjir Sinyal - ketidakmampuan memproses esensi di tengah kebisingan',
      Family:
        'Mumi Cinta - mengawetkan hubungan dalam bentuk kaku tanpa pertumbuhan',
      Religion:
        'Kandang Keyakinan - terpenjara dalam dogma tanpa ruang bernafas',
      Education:
        'Kutukan Kepastian - kehilangan keajaiban dalam proses mencari jawaban',
      Government:
        'Mesin Birokrasi - menjadi bagian dari sistem yang ingin diubah',
    };

    return warnings[heart] || 'Risiko kehilangan keseimbangan dalam passion';
  }

  /**
   * Enhanced Abilities Analysis Methods based on Abilities.md guidance
   */

  /**
   * Get Hidden Strengths for Abilities
   */
  private getAbilityHiddenStrengths(ability: string): string {
    const strengths: { [key: string]: string } = {
      'Cognitive Skills':
        'Meta-thinking - berpikir tentang cara berpikir untuk mengoptimalkan proses kognitif',
      'Social Skills':
        'Social sensing - merasakan dinamika kelompok dan menyesuaikan komunikasi',
      'Technical Skills':
        'Tech translation - menerjemahkan kebutuhan bisnis menjadi solusi teknis',
      'Creative Skills':
        'Creative synthesis - menggabungkan elemen tak terduga menjadi solusi inovatif',
      'Leadership Skills':
        'Adaptive leadership - menyesuaikan gaya kepemimpinan dengan situasi dan tim',
      'Communication Skills':
        'Message crafting - menyesuaikan pesan dengan audiens dan konteks',
    };

    return (
      strengths[ability] ||
      'Kemampuan unik untuk menciptakan dampak dalam bidang ini'
    );
  }

  /**
   * Get Love Language for Abilities
   */
  private getAbilityLoveLanguage(ability: string): string[] {
    const loveLanguages: { [key: string]: string[] } = {
      'Cognitive Skills': [
        'Tantangan intelektual',
        'Data berkualitas',
        'Waktu refleksi',
      ],
      'Social Skills': [
        'Authentic connection',
        'Collaborative environment',
        'Recognition of impact',
      ],
      'Technical Skills': [
        'Cutting-edge tools',
        'Learning resources',
        'Implementation freedom',
      ],
      'Creative Skills': [
        'Creative freedom',
        'Diverse inspiration',
        'Constructive feedback',
      ],
      'Leadership Skills': [
        'Trust and autonomy',
        'Meaningful mission',
        'Growth opportunities',
      ],
      'Communication Skills': [
        'Active listening',
        'Platform variety',
        'Feedback loop',
      ],
    };

    return (
      loveLanguages[ability] || [
        'Recognition',
        'Growth opportunities',
        'Meaningful work',
      ]
    );
  }

  /**
   * Get Mythical Figures for Abilities inspiration
   */
  private getAbilityMythicalFigures(ability: string): string[] {
    const figures: { [key: string]: string[] } = {
      'Cognitive Skills': ['Sherlock Holmes', 'Athena', 'Alan Turing'],
      'Social Skills': ['Hermes', 'Gandhi', 'Oprah Winfrey'],
      'Technical Skills': ['Hephaestus', 'Steve Wozniak', 'Ada Lovelace'],
      'Creative Skills': ['Leonardo da Vinci', 'Frida Kahlo', 'Walt Disney'],
      'Leadership Skills': [
        'Nelson Mandela',
        'Joan of Arc',
        'Servant Leadership Philosophy',
      ],
      'Communication Skills': ['Cicero', 'Maya Angelou', 'TED Speakers'],
    };

    return (
      figures[ability] || [
        'Wise Mentor',
        'Skilled Practitioner',
        'Innovative Leader',
      ]
    );
  }

  /**
   * Get Warning Signs for Abilities
   */
  private getAbilityWarnings(ability: string): string {
    const warnings: { [key: string]: string } = {
      'Cognitive Skills':
        'Analysis Paralysis - terjebak dalam analisis tanpa mengambil tindakan',
      'Social Skills': 'People Pleasing - kehilangan autentisitas demi harmoni',
      'Technical Skills':
        'Tool Obsession - fokus pada teknologi tanpa mempertimbangkan kebutuhan user',
      'Creative Skills':
        'Creative Chaos - kehilangan fokus dalam eksplorasi tanpa batas',
      'Leadership Skills':
        'Hero Complex - merasa harus menyelesaikan semua masalah sendiri',
      'Communication Skills':
        'Message Overload - terlalu banyak berkomunikasi tanpa substansi',
    };

    return warnings[ability] || 'Risiko overextending dalam bidang ini';
  }

  /**
   * Get Creative Ideas for Abilities
   */
  private getAbilityCreativeIdeas(ability: string): string[] {
    const ideas: { [key: string]: string[] } = {
      'Cognitive Skills': [
        'Cognitive Bootcamp',
        'Pattern Recognition Games',
        'Logic Escape Room',
      ],
      'Social Skills': [
        'Social Experiment Lab',
        'Conflict Resolution Theater',
        'Empathy Mapping Workshop',
      ],
      'Technical Skills': [
        'Tech for Good Hackathon',
        'Digital Minimalism Challenge',
        'Legacy System Revival',
      ],
      'Creative Skills': [
        'Constraint-based Creativity',
        'Cross-pollination Workshop',
        'Failure Gallery',
      ],
      'Leadership Skills': [
        'Reverse Leadership',
        'Crisis Simulation',
        'Values-based Decision Making',
      ],
      'Communication Skills': [
        'Silent Communication Challenge',
        'Multi-modal Storytelling',
        'Difficult Conversation Practice',
      ],
    };

    return (
      ideas[ability] || [
        'Innovative Projects',
        'Skill Challenges',
        'Collaborative Experiments',
      ]
    );
  }

  /**
   * Get Ability Combination Profiles
   */
  private getAbilityCombinationProfiles(topAbilities: any[]): any[] {
    if (topAbilities.length < 2) return [];

    const combinations: { [key: string]: any } = {
      'Cognitive Skills + Social Skills': {
        profile: 'Social Analyst',
        description:
          'Menggunakan analisis data untuk memahami dan meningkatkan dinamika sosial',
        projects: [
          'Social Network Analysis',
          'Behavioral Economics Research',
          'Data-driven Community Building',
        ],
        metaphor: 'Detektif Hubungan Manusia',
      },
      'Technical Skills + Creative Skills': {
        profile: 'Creative Technologist',
        description: 'Menggabungkan keahlian teknis dengan inovasi kreatif',
        projects: [
          'Interactive Art Installations',
          'Creative Coding Workshops',
          'Design-Tech Fusion',
        ],
        metaphor: 'Digital Artist',
      },
      'Leadership Skills + Communication Skills': {
        profile: 'Inspirational Leader',
        description: 'Memimpin melalui komunikasi yang powerful dan inspiring',
        projects: [
          'Thought Leadership Platform',
          'Public Speaking Mastery',
          'Change Communication',
        ],
        metaphor: 'Voice of Change',
      },
    };

    const topTwo = topAbilities.slice(0, 2);
    const combinationKey = `${topTwo[0].name} + ${topTwo[1].name}`;
    const reverseKey = `${topTwo[1].name} + ${topTwo[0].name}`;

    return [
      combinations[combinationKey] ||
        combinations[reverseKey] || {
          profile: 'Unique Combination',
          description: `Kombinasi unik antara ${topTwo[0].name} dan ${topTwo[1].name}`,
          projects: [
            'Cross-functional Projects',
            'Innovative Solutions',
            'Hybrid Approaches',
          ],
          metaphor: 'Multi-dimensional Professional',
        },
    ];
  }
}
