import { Injectable, inject } from '@angular/core';
import { ReportProcessorService } from './report-processor-new.service';
import { DomSanitizer } from '@angular/platform-browser';
import {
  ShapeRawData,
  ShapePdfReportData,
  ShapeUser,
  StrengthDescription,
  HeartDescription,
  AbilitiesDescription,
  PersonalityCharacteristics,
  ExperienceDescription,
  IntegrationData,
} from '../models/shape-report.types';

@Injectable({
  providedIn: 'root',
})
export class ShapePdfReportService {
  private reportProcessor = inject(ReportProcessorService);
  private sanitizer = inject(DomSanitizer);

  /**
   * Prepare data for PDF report generation
   * @param assessmentData Raw assessment data
   * @returns Processed data ready for PDF generation
   */
  preparePdfReportData(
    assessmentData: ShapeRawData
  ): ShapePdfReportData | null {
    if (!assessmentData) {
      return null;
    }

    // Process the raw assessment data
    const processedReport =
      this.reportProcessor.processShapeReport(assessmentData);

    if (!processedReport) {
      return null;
    }

    // Format data specifically for PDF structure
    return {
      user: {
        name: assessmentData.user?.name || 'User',
        email: assessmentData.user?.email || '',
        assessment_date:
          assessmentData.user?.assessment_date ||
          new Date().toISOString().split('T')[0],
      },

      // Profile Metaphor
      profileMetaphor: this.generateProfileMetaphor(processedReport),

      // Strength data
      strength: {
        top_abilities: this.extractTopStrengths(assessmentData.strength),
        scores: assessmentData.strength?.scores || {},
        description: this.getStrengthDescription(processedReport),
      },

      // Heart data
      heart: {
        top_interests: this.extractTopInterests(assessmentData.heart),
        scores: assessmentData.heart?.scores || {},
        description: this.getHeartDescription(processedReport),
      },

      // Abilities data
      abilities: {
        cognitive: assessmentData.abilities?.cognitive || 0,
        social: assessmentData.abilities?.social || 0,
        technical: assessmentData.abilities?.technical || 0,
        management: assessmentData.abilities?.management || 0,
        creative: assessmentData.abilities?.creative || 0,
        physical: assessmentData.abilities?.physical || 0,
        description: this.getAbilitiesDescription(processedReport),
      },

      // Personality data
      personality: {
        primary: assessmentData.personality?.primary || '',
        secondary: assessmentData.personality?.secondary || '',
        scores: assessmentData.personality?.scores || {},
        characteristics: this.getPersonalityCharacteristics(processedReport),
      },

      // Experience data
      experience: {
        core_competencies: assessmentData.experience?.core_competencies || [],
        transformative_moments:
          assessmentData.experience?.transformative_moments || [],
        description: this.getExperienceDescription(processedReport),
      },

      // Integration components
      integration: {
        genius_zone: processedReport.genius_zone || 'Specialized Professional',
        pattern_description:
          this.getIntegrationPatternDescription(processedReport),
        development_recommendations:
          this.getDevelopmentRecommendations(processedReport),
      },

      // Color scheme for consistent styling
      colors: {
        strength: '#FF6B6B',
        heart: '#4ECDC4',
        abilities: '#FFE66D',
        personality: '#1A535C',
        experience: '#FF9F1C',
      },
    };
  }

  /**
   * Generate a profile metaphor based on the user's dominant traits
   */
  private generateProfileMetaphor(report: any): string {
    const strength = report.strength_profile?.primary || '';
    const heart = report.heart_profile?.primary_interest || '';

    const strengthArchetypes: { [key: string]: string } = {
      'Visionary Pioneers': 'Pemikir Visioner',
      'Insightful Truth-Seekers': 'Pencari Kebenaran',
      'Inspiring Connectors': 'Inspirator',
      'Supportive Nurturers': 'Pendukung',
      'Clarifying Mentors': 'Penjelasan Sistematis',
    };

    const heartDomains: { [key: string]: string } = {
      'Art & Entertainment': 'dunia Seni & Hiburan',
      Business: 'dunia Bisnis',
      'Communication/Media': 'dunia Media & Komunikasi',
      Family: 'konteks Keluarga',
      Religion: 'ruang Spiritual',
      Education: 'dunia Pendidikan',
      Government: 'ruang Kebijakan Publik',
    };

    const archetype = strengthArchetypes[strength] || 'Pemikir Strategis';
    const domain = heartDomains[heart] || 'bidang profesional';

    return `Anda adalah ${archetype} di ${domain}`;
  }

  /**
   * Extract top strength abilities
   */
  private extractTopStrengths(strengthData: any): string[] {
    if (!strengthData || !strengthData.scores) {
      return ['Visionary Pioneers', 'Clarifying Mentors'];
    }

    // Sort scores and get top 2
    return Object.entries(strengthData.scores)
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 2)
      .map(([key]: any) => key);
  }

  /**
   * Extract top heart interests
   */
  private extractTopInterests(heartData: any): string[] {
    if (!heartData || !heartData.scores) {
      return ['Education', 'Art & Entertainment'];
    }

    // Sort scores and get top 2
    return Object.entries(heartData.scores)
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 2)
      .map(([key]: any) => key);
  }

  /**
   * Get strength description
   */
  private getStrengthDescription(report: any): any {
    const primary = report.strength_profile?.primary || '';
    const secondary = report.strength_profile?.secondary || '';

    return {
      primary,
      secondary,
      primaryDescription: `${primary}: ${this.getStrengthDefinition(primary)}`,
      secondaryDescription: `${secondary}: ${this.getStrengthDefinition(
        secondary
      )}`,
      synergy: this.getStrengthSynergy(primary, secondary),
    };
  }

  /**
   * Get heart description
   */
  private getHeartDescription(report: any): any {
    const primary = report.heart_profile?.primary_interest || '';
    const secondary = report.heart_profile?.secondary_interest || '';

    return {
      primary,
      secondary,
      primaryDescription: this.getHeartDefinition(primary),
      secondaryDescription: this.getHeartDefinition(secondary),
      synergy: `${primary} + ${secondary}: ${this.getHeartSynergy(
        primary,
        secondary
      )}`,
    };
  }

  /**
   * Get abilities description
   */
  private getAbilitiesDescription(report: any): any {
    // Get top 2 abilities
    const abilitiesMap = report.abilities_profile?.scores || {};
    const topAbilities = Object.entries(abilitiesMap)
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 2)
      .map(([key]: any) => key);

    return {
      top: topAbilities,
      descriptions: topAbilities.map(
        (ability) => `${ability}: ${this.getAbilityDefinition(ability)}`
      ),
    };
  }

  /**
   * Get personality characteristics
   */
  private getPersonalityCharacteristics(report: any): any {
    const primary = report.personality_profile?.primary_type || '';
    const secondary = report.personality_profile?.secondary_type || '';

    return {
      profile: `${primary} + ${secondary}`,
      archetype: this.getPersonalityArchetype(primary, secondary),
      strengths: this.getPersonalityStrengths(primary),
      challenges: this.getPersonalityChallenges(primary),
    };
  }

  /**
   * Get experience description
   */
  private getExperienceDescription(report: any): any {
    return {
      core_patterns: report.experience_profile?.core_patterns || [
        'Manajemen Proyek',
        'Pelatihan',
      ],
      transformative_experiences:
        report.experience_profile?.transformative_experiences || [],
      skill_development:
        report.experience_profile?.skill_development ||
        'Pengembangan keahlian berbasis proyek',
    };
  }

  /**
   * Get integration pattern description
   */
  private getIntegrationPatternDescription(report: any): string {
    const strength = report.strength_profile?.primary || '';
    const heart = report.heart_profile?.primary_interest || '';
    const ability = Object.entries(report.abilities_profile?.scores || {})
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 1)
      .map(([key]: any) => key)[0];

    return `Passion di bidang ${heart} menjadi bahan bakar pengembangan ${ability}, yang diperkuat oleh ${strength} bawaan.`;
  }

  /**
   * Get development recommendations
   */
  private getDevelopmentRecommendations(report: any): any {
    const strength = report.strength_profile?.primary || '';
    const heart = report.heart_profile?.primary_interest || '';
    const abilities = Object.entries(report.abilities_profile?.scores || {})
      .sort(([, a]: any, [, b]: any) => b - a)
      .slice(0, 1)
      .map(([key]: any) => key)[0];
    const personality = report.personality_profile?.primary_type || '';

    return {
      strengthen: {
        title: `Kuatkan (Strength × Ability)`,
        recommendations: [
          this.getStrengthAbilityRecommendation(strength, abilities),
          `Proyek: Develop modul pelatihan inovatif`,
        ],
      },
      explore: {
        title: `Eksplorasi (Heart × Experience)`,
        recommendations: [
          this.getHeartExperienceRecommendation(heart),
          `Studi kasus: "Seni sebagai Media Edukasi"`,
        ],
      },
      adapt: {
        title: `Adaptasi (Personality)`,
        recommendations: [this.getPersonalityRecommendation(personality)],
      },
      roadmap: {
        phases: [
          {
            name: 'Penguatan',
            activity: 'Pelatihan Manajemen Edukasi',
            duration: '90 hari',
          },
          {
            name: 'Eksplorasi',
            activity: 'Proyek Seni-Edukasi',
            duration: '60 hari',
          },
          {
            name: 'Konsolidasi',
            activity: 'Membuat Portofolio',
            duration: '30 hari',
          },
        ],
      },
    };
  }

  /**
   * Helper function definitions for various components
   */
  private getStrengthDefinition(strength: string): string {
    const definitions: { [key: string]: string } = {
      'Visionary Pioneers':
        'Kemampuan melihat peluang baru dan memulai terobosan',
      'Insightful Truth-Seekers':
        'Kemampuan menemukan inti kebenaran dan prinsip fundamental',
      'Inspiring Connectors':
        'Kemampuan menghubungkan orang dengan ide dan peluang',
      'Supportive Nurturers': 'Kemampuan mendukung pertumbuhan orang lain',
      'Clarifying Mentors':
        'Kemampuan menjelaskan konsep kompleks secara sistematis',
    };

    return definitions[strength] || 'Kemampuan unik yang perlu dikembangkan';
  }

  private getHeartDefinition(interest: string): string {
    const definitions: { [key: string]: string } = {
      'Art & Entertainment': 'Minat pada seni, budaya, dan industri hiburan',
      Business: 'Minat pada kewirausahaan, manajemen, dan pengembangan bisnis',
      'Communication/Media':
        'Minat pada jurnalistik, media, dan komunikasi publik',
      Family: 'Minat pada penguatan keluarga dan hubungan personal',
      Religion: 'Minat pada spiritualitas, agama, dan makna hidup',
      Education: 'Minat pada pembelajaran, pengajaran, dan pengembangan ilmu',
      Government:
        'Minat pada kebijakan publik, pelayanan masyarakat, dan tata kelola',
    };

    return (
      definitions[interest] || 'Area minat yang memberi energi dan motivasi'
    );
  }

  private getAbilityDefinition(ability: string): string {
    const definitions: { [key: string]: string } = {
      cognitive:
        'Kemampuan analisis, penalaran, dan pemecahan masalah kompleks',
      social: 'Kemampuan memahami orang, berkolaborasi, dan membangun hubungan',
      technical: 'Kemampuan menguasai sistem, alat, dan teknologi',
      management: 'Kemampuan mengorganisasi sumber daya, proses, dan orang',
      creative: 'Kemampuan inovasi, desain, dan kreasi konten orisinal',
      physical:
        'Kemampuan ketangkasan, keterampilan tangan, dan aktivitas fisik',
    };

    return definitions[ability] || 'Keahlian praktis yang dapat ditingkatkan';
  }

  private getStrengthSynergy(primary: string, secondary: string): string {
    const synergyPairs: { [key: string]: string } = {
      'Visionary Pioneers_Insightful Truth-Seekers':
        'Innovator Strategis - visi radikal berbasis prinsip fundamental',
      'Inspiring Connectors_Supportive Nurturers':
        'Community Architect - membangun ekosistem saling mendukung',
      'Clarifying Mentors_Insightful Truth-Seekers':
        'Systems Philosopher - kerangka kerja berbasis kebijaksanaan',
      'Visionary Pioneers_Inspiring Connectors':
        'Movement Starter - mengubah ide menjadi gerakan masif',
      'Supportive Nurturers_Clarifying Mentors':
        'Talent Alchemist - mengubah potensi mentah menjadi keunggulan',
    };

    const key = `${primary}_${secondary}`;
    return (
      synergyPairs[key] || 'Kombinasi unik yang menguatkan keunggulan personal'
    );
  }

  private getHeartSynergy(primary: string, secondary: string): string {
    const synergyPairs: { [key: string]: string } = {
      'Education_Art & Entertainment':
        'Pendidikan Kreatif - pembelajaran melalui eksplorasi artistik',
      Business_Education:
        'Knowledge Enterprise - monetisasi pengetahuan dan keahlian',
      'Communication/Media_Government':
        'Public Affairs Specialist - menjembatani kebijakan dan masyarakat',
      Family_Religion:
        'Pembina Nilai - menanamkan prinsip hidup melalui hubungan erat',
    };

    const key = `${primary}_${secondary}`;
    return (
      synergyPairs[key] || 'Kombinasi minat yang menciptakan keunikan personal'
    );
  }

  private getPersonalityArchetype(primary: string, secondary: string): string {
    const archetypes: { [key: string]: string } = {
      'Visionary Driver_Precision Analyst':
        'Strategist: Pemimpin visioner dengan pendekatan analitis',
      'Dynamic Connector_Visionary Driver':
        'Catalyst: Penggerak perubahan yang menginspirasi orang lain',
      'Steady Supporter_Precision Analyst':
        'Guardian: Penjaga kualitas dengan pendekatan stabil',
      'Dynamic Connector_Steady Supporter':
        'Diplomat: Pembangun hubungan dengan fondasi kuat',
    };

    const key = `${primary}_${secondary}`;
    return archetypes[key] || 'Profil unik dengan kombinasi gaya personal';
  }

  private getPersonalityStrengths(personalityType: string): string[] {
    const strengths: { [key: string]: string[] } = {
      'Visionary Driver': [
        'Inisiatif tinggi',
        'Berorientasi hasil',
        'Pemikiran strategis',
      ],
      'Dynamic Connector': [
        'Keterampilan sosial tinggi',
        'Membangun koneksi cepat',
        'Energi positif',
      ],
      'Steady Supporter': ['Konsistensi tinggi', 'Kesetiaan', 'Keandalan'],
      'Precision Analyst': [
        'Perhatian pada detail',
        'Kualitas tinggi',
        'Analisis mendalam',
      ],
    };

    return (
      strengths[personalityType] || [
        'Kombinasi kekuatan unik',
        'Adaptabilitas situasional',
      ]
    );
  }

  private getPersonalityChallenges(personalityType: string): string[] {
    const challenges: { [key: string]: string[] } = {
      'Visionary Driver': [
        'Kesulitan dengan detail operasional',
        'Kurang sabar',
        'Terkesan dominan',
      ],
      'Dynamic Connector': [
        'Terlalu bergantung pada hubungan',
        'Menghindari konflik',
        'Kurang fokus',
      ],
      'Steady Supporter': [
        'Sulit menghadapi perubahan cepat',
        'Menunda keputusan',
        'Kurang tegas',
      ],
      'Precision Analyst': [
        'Perfeksionisme berlebihan',
        'Kesulitan melihat gambaran besar',
        'Terkesan kritis',
      ],
    };

    return (
      challenges[personalityType] || [
        'Potensi ketidakseimbangan',
        'Area untuk refleksi',
      ]
    );
  }

  private getStrengthAbilityRecommendation(
    strength: string,
    ability: string
  ): string {
    const recommendations: { [key: string]: { [key: string]: string } } = {
      'Visionary Pioneers': {
        management: 'Kursus "Manajemen Inovasi dan Pengembangan Produk"',
        cognitive: 'Workshop "Strategic Foresight & Scenario Planning"',
        creative: 'Program "Design Thinking for Disruptive Innovation"',
      },
      'Clarifying Mentors': {
        management: 'Kursus "Manajemen Program Pendidikan"',
        cognitive: 'Sertifikasi "Instructional Design & Learning Architecture"',
        creative: 'Workshop "Visual Learning & Knowledge Mapping"',
      },
    };

    return (
      recommendations[strength]?.[ability] ||
      'Kursus pengembangan profesional berbasis kekuatan'
    );
  }

  private getHeartExperienceRecommendation(heart: string): string {
    const recommendations: { [key: string]: string } = {
      Education: 'Kolaborasi dengan komunitas seni-edukasi',
      'Art & Entertainment':
        'Eksplorasi pendekatan terapi seni untuk pembelajaran',
      Business: 'Mentoring startup di industri kreatif',
      Government: 'Keterlibatan dalam program kebijakan pendidikan publik',
    };

    return recommendations[heart] || 'Proyek pengembangan berbasis passion';
  }

  private getPersonalityRecommendation(personality: string): string {
    const recommendations: { [key: string]: string } = {
      'Visionary Driver':
        'Pelatihan manajemen detail untuk melengkapi visi besar',
      'Dynamic Connector':
        'Workshop fokus dan penyelesaian tugas untuk menyeimbangkan kegiatan sosial',
      'Steady Supporter':
        'Program pengembangan ketegasan dan pengambilan keputusan cepat',
      'Precision Analyst':
        'Pelatihan komunikasi visual untuk menyederhanakan ide kompleks',
    };

    return (
      recommendations[personality] ||
      'Program pengembangan untuk menyeimbangkan profil kepribadian'
    );
  }
}
