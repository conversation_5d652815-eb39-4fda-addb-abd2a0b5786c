<!-- Loading State -->
<div *ngIf="isLoading" class="min-h-screen bg-gray-50 flex items-center justify-center">
  <div class="text-center">
    <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
    <p class="mt-4 text-lg text-gray-600">Loading your SHAPE report...</p>
  </div>
</div>

<!-- Error State -->
<div *ngIf="hasError && !isLoading" class="min-h-screen bg-gray-50 flex items-center justify-center">
  <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
    <div class="text-center">
      <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
      <h3 class="mt-2 text-lg font-medium text-gray-900">Assessment Incomplete</h3>
      <p class="mt-1 text-sm text-gray-500">{{ errorMessage }}</p>
      
      <!-- Show incomplete areas if available -->
      <div *ngIf="completionStatus && !completionStatus.allComplete" class="mt-4">
        <p class="text-sm text-gray-600 mb-2">Please complete these assessments:</p>
        <div class="space-y-2">
          <button 
            *ngFor="let area of getIncompleteAreas()"
            (click)="navigateToAssessment(getAssessmentIdByArea(area))"
            class="block w-full px-4 py-2 text-sm text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition duration-150"
          >
            Complete {{ area | titlecase }} Assessment
          </button>
        </div>
      </div>
      
      <div class="mt-6">
        <button 
          (click)="goToDashboard()"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Dashboard
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main Results Display -->
<div *ngIf="!isLoading && !hasError && processedReport" class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Your SHAPE Report</h1>
          <p class="mt-1 text-sm text-gray-500">Comprehensive analysis of your unique profile</p>
        </div>
        <div class="flex space-x-3">
          <button 
            (click)="generatePdfReport()"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download PDF
          </button>
          <button 
            (click)="goToDashboard()"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <button 
          *ngFor="let tab of ['overview', 'strength', 'heart', 'abilities', 'personality', 'experience']"
          (click)="switchTab(tab)"
          [class]="activeTab === tab ? 
            'border-indigo-500 text-indigo-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm' : 
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'"
        >
          {{ tab | titlecase }}
        </button>
      </nav>
    </div>
  </div>

  <!-- Content Area -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Overview Tab -->
    <div *ngIf="activeTab === 'overview'" class="space-y-8">
      
      <!-- Integrative Profile Card -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Your Integrative Profile</h3>
          <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-6">
            <blockquote class="text-lg italic text-gray-700 mb-4">
              "{{ getIntegrativeProfile() }}"
            </blockquote>
            <p class="text-sm text-gray-600">{{ getWisdomQuote() }}</p>
          </div>
        </div>
      </div>

      <!-- Quick Summary Grid -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        
        <!-- Strength Card -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span class="text-red-600 font-bold">S</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Primary Strength</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ getPrimaryStrength() }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- Heart Card -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                  <span class="text-pink-600 font-bold">H</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Primary Passion</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ getPrimaryHeart() }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- Abilities Card -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span class="text-yellow-600 font-bold">A</span>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Top Abilities</dt>
                  <dd class="text-sm font-medium text-gray-900">
                    <span *ngFor="let ability of getTopAbilities().slice(0, 2); let last = last">
                      {{ ability }}<span *ngIf="!last">, </span>
                    </span>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- Genius Zone -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Your Genius Zone</h3>
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <p class="text-green-800">{{ getGeniusZone() }}</p>
          </div>
        </div>
      </div>

      <!-- Development Recommendations -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Development Recommendations</h3>
          <ul class="space-y-2">
            <li *ngFor="let recommendation of getDevelopmentRecommendations()" class="flex items-start">
              <svg class="flex-shrink-0 h-5 w-5 text-green-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 text-sm text-gray-700">{{ recommendation }}</span>
            </li>
          </ul>
        </div>
      </div>

    </div>

    <!-- Individual Assessment Tabs -->
    <div *ngIf="activeTab !== 'overview'" class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          {{ activeTab | titlecase }} Analysis
        </h3>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Detailed Analysis Coming Soon</h3>
          <p class="mt-1 text-sm text-gray-500">
            Comprehensive {{ activeTab }} analysis will be displayed here.
          </p>
        </div>
      </div>
    </div>

  </div>
</div>
