import { Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { LoginComponent } from './pages/auth/login.component';
import { SignupComponent } from './pages/auth/signup.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { AdminDashboardComponent } from './pages/admin/admin-dashboard.component';
import { AssessmentListComponent } from './pages/assessments/assessment-list.component';
import { AssessmentDetailComponent } from './pages/assessments/assessment-detail.component';

import { AboutComponent } from './pages/about/about.component';
import { ProfileComponent } from './pages/profile/profile.component';
import { authGuard } from './shared/guards/auth.guard';
import { NotFoundComponent } from './pages/not-found/not-found.component';

// Admin guard function for checking if user is an admin
export function adminGuard(): boolean {
  // In a real app, this would check if the current user has admin role
  // For now, we'll just return true to allow access
  return true;
}

export const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'login', component: LoginComponent },
  { path: 'signup', component: SignupComponent },
  { path: 'about', component: AboutComponent },
  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [authGuard],
  },
  {
    path: 'admin',
    component: AdminDashboardComponent,
    // canActivate: [authGuard],
    // In a real app, use proper admin guard:
    // canActivate: [authGuard, () => adminGuard()],
  },
  {
    path: 'assessments',
    component: AssessmentListComponent,
    canActivate: [authGuard],
  },
  {
    path: 'assessments/:id',
    component: AssessmentDetailComponent,
    canActivate: [authGuard],
  },
  { path: 'profile', component: ProfileComponent, canActivate: [authGuard] },
  { path: 'not-found', component: NotFoundComponent },
  { path: '404', component: NotFoundComponent },
  { path: '**', component: NotFoundComponent },
];
